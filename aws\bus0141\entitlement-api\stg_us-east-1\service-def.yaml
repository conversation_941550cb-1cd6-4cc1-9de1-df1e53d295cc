launchType: FARGATE
serviceName: stg-entitlement-api-service-nlb
desiredCount: 2
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-03afef83e7d6959c8 # private_app
      - sg-08a83e28c2216dc3c # private_web
      - sg-0fd2a98817ac752f7 # private_db
    subnets:
      - subnet-08f0ece89d42f7d73 # us-east-1c-private
      - subnet-0a1c0ff43ccc5652c # us-east-1b-private
      - subnet-00fa9e860f858a4a2 # us-east-1a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  minimumHealthyPercent: 50

healthCheckGracePeriodSeconds: 300

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: stg-entitlement-api
    containerPort: 8443

tags:
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: ORG
    value: <+serviceVariables.ORG>
  - key: TID
    value: <+variable.TID>
  - key: PID
    value: <+serviceVariables.PID>
  - key: MANAGED
    value: <+serviceVariables.MANAGED>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>

propagateTags: TASK_DEFINITION