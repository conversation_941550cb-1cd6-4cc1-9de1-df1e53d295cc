deploy_role_name: mstar-engr-cross-account-deploy

ecs_services:
  - service_name: fundapi-v2-service-dev
    cluster_name: fundapi-cluster-dev
    autoscaling: true
    min_capacity: 1
  - service_name: fundapi-v1-service-dev
    cluster_name: fundapi-cluster-dev
    autoscaling: true
    min_capacity: 1
  - service_name: apicenter-service-dev
    cluster_name: fundapi-cluster-dev
    autoscaling: false
    min_capacity: 1
  - service_name: apicenter-backend-service-dev
    cluster_name: fundapi-cluster-dev
    autoscaling: false
    min_capacity: 1
  - service_name: apicenter-entitlement-service-dev
    cluster_name: fundapi-cluster-dev
    autoscaling: false
    min_capacity: 1

route53:
  zone_id: Z1763QDC3XWVS7
  location: 'templates/route53.json.j2'
  domains:
    - domain: apicenter-dev.date7ebe.easn.morningstar.com
      records:
        - set_identifier: dev
          region: us-east-1
        - set_identifier: dev-dr
          region: us-west-2
    - domain: apicenter-backend-dev.date7ebe.easn.morningstar.com
      records:
        - set_identifier: dev
          region: us-east-1
        - set_identifier: dev-dr
          region: us-west-2
    - domain: fundapi-dev.date7ebe.easn.morningstar.com
      records:
        - set_identifier: dev
          region: us-east-1
        - set_identifier: dev-dr
          region: us-west-2