region                = "us-west-2"
dr_region             = "us-west-2"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "nonprod"
environmentForTagging = "non-prd"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################
load_balancers = {
  apicenter_alb = {
    application_name      = "apicenter",
    is_internal           = true,
    idle_timeout          = 300
    enable_access_logs    = false,
    extra_security_groups = ["private_postfeed"]
  }
  fundapi_alb = {
    application_name      = "fundapi",
    is_internal           = true,
    idle_timeout          = 4000
    enable_access_logs    = false,
    extra_security_groups = ["private_postfeed"]
  }
}
route53_name             = "date7ebe.easn.morningstar.com"
bucket_name              = "apicenter-accesslog-us-west-2-nonprod"
data_bucket_name         = "apicenter-data-us-west-2-nonprod"
force_destroy            = true
apicenter_vo_alert_count = 0
cache_node_type          = "cache.t3.small"
cache_num_node_groups    = 1