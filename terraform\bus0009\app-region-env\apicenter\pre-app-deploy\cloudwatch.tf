resource "aws_cloudwatch_log_group" "main" {
  name              = "/aws/ecs/${local.task_family_name}"
  retention_in_days = var.log_retention_days
  tags              = local.tags
}

resource "aws_cloudwatch_log_subscription_filter" "filter" {
  name            = "${var.name}-log-subscription-${var.environment}"
  log_group_name  = aws_cloudwatch_log_group.main.name
  filter_pattern  = ""
  destination_arn = data.aws_kinesis_stream.stream.arn
  role_arn        = data.aws_iam_role.log_role.arn
}

