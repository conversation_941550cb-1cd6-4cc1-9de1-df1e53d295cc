resource "aws_cloudwatch_log_group" "apicenter_log_group" {
  name              = "/aws/ecs/${var.application_name}-${var.environment}"
  retention_in_days = var.log_retention_days
  tags              = local.tags
}

resource "aws_cloudwatch_log_subscription_filter" "apicenter_log_subscription_filter" {
  name            = "${var.application_name}-log-subscription-${var.environment}"
  log_group_name  = aws_cloudwatch_log_group.apicenter_log_group.name
  filter_pattern  = ""
  destination_arn =  data.aws_kinesis_stream.stream.arn
  role_arn        = data.aws_iam_role.log_role.arn
}

