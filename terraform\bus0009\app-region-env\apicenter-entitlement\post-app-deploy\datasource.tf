data "aws_lb" "apicenter_lb" {
  name = local.alb_map[var.environment]
}

data "aws_ecs_cluster" "fund_api_cluster" {
  cluster_name = "fundapi-cluster-${var.environment}"
}

data "aws_ecs_service" "entitlement_service" {
  service_name = "${var.application_name}-service-${var.environment}"
  cluster_arn  = data.aws_ecs_cluster.fund_api_cluster.arn
}

data "aws_iam_role" "mart-role" {
  name = local.mart_role_map[var.environment]
}

data "aws_route53_zone" "main" {
  name = var.route53_name
  private_zone = false
}