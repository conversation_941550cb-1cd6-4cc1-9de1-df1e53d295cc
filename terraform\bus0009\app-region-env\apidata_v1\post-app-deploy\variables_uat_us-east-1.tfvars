region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "uat"
environmentForTagging = "uat"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

min_count                    = 1
max_count                    = 2
peak_min_count               = 1
peak_max_count               = 3
ecs_task_schedule_up         = "cron(50 5 ? * MON,TUE,WED,THU,FRI *)"
ecs_task_schedule_down       = "cron(5 4 ? * MON,TUE,WED,THU,FRI *)"
cloudwatch_evaluation_period = 1
cloudwatch_period            = 180
enable_vo_alert              = false