region                = "us-west-2"
dr_region             = "us-west-2"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "prod"
environmentForTagging = "prd"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################
load_balancers = {
  apicenter_alb = {
    application_name      = "apicenter",
    is_internal           = false,
    idle_timeout          = 300
    enable_access_logs    = false,
    extra_security_groups = ["private_postfeed"]
  }
  fundapi_alb = {
    application_name      = "fundapi",
    is_internal           = true,
    idle_timeout          = 4000
    enable_access_logs    = true,
    extra_security_groups = ["private_postfeed"]
  }
  backend_alb = {
    application_name   = "apicenter-backend",
    is_internal        = true,
    idle_timeout       = 300
    enable_access_logs = false
  }
}
route53_name             = "dat688b3.eas.morningstar.com"
bucket_name              = "apicenter-accesslog-us-west-2-prod"
data_bucket_name         = "apicenter-data-us-west-2-prod"
force_destroy            = true
apicenter_vo_alert_count = 1
cache_node_type          = "cache.r7g.xlarge"
cache_num_node_groups    = 2
vo_endpoint              = "https://alert.victorops.com/integrations/cloudwatch/20131130/alert/3da4a2a2-477d-4cdd-8dab-1d1e76c25a3d/Bus0009"