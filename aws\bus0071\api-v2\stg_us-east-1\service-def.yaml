launchType: FARGATE
serviceName: ts00804-stg-eqapi-restful-api
desiredCount: 1
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-eac4a796 # private_app
      - sg-ebc4a797 # private_web
    subnets:
      - subnet-d20a5cff
      - subnet-9a91dcc1
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 50

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: stg-restful-api-container
    containerPort: 80