module "region_data" {
  source = "git::ssh://msstash.morningstar.com/cloudsvc/tf-module-infrastructure-data.git?ref=feature/terraform1.3"
}


data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

data "aws_iam_role" "mart-role" {
  name = "mart-role-${var.environment == "dev" ? "dev" : "prod"}"
}

data "aws_iam_role" "log_role" {
  name = "CWLtoKinesis"
}

data "aws_kinesis_stream" "stream" {
  name = "mstar-kinesis-dataac-${var.environment == "dev" ? "non-prod" : "prod"}-splunk-${var.region}"
}

data "aws_sns_topic" "clear_cache_sns" {
  name = "clear-cache-topic-${var.environment == "dev" ? "stg" : "prod"}"
}