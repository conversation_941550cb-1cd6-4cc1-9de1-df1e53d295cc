module "vpc" {
  source  = "../vpc"
  vpc_tag = var.vpc_tag
}

module "private_subnets" {
  source              = "../subnets"
  vpc_id              = module.vpc.vpc_id
  filter_tag_function = "PUBLIC"
}

module "sec_groups" {
  source           = "../security-groups"
  security_groups  = ["private_app","private_web"]
}

module "log_group" {
  source     = "../cloudwatch-logs"
  log_group  = var.log_group
  tags       = merge(var.tags)
}

module "load_balance" {
  source                     = "../load-balance-v2"
  access_log_bucket          = var.access_log_bucket
  access_log_prefix          = var.access_log_prefix
  alb_health_check_interval  = var.alb_health_check_interval
  alb_health_check_path      = var.alb_health_check_path
  alb_health_check_timeout   = var.alb_health_check_timeout
  alb_healthy_threshold      = var.alb_healthy_threshold
  alb_unhealthy_threshold    = var.alb_unhealthy_threshold
  certificate_domain         = var.certificate_domain
  create_resource            = var.create_resource
  tags                       = var.tags
  vpc_tag                    = var.vpc_tag

  app_name = var.app_name
}

module "auto_scaling_policy" {
  source                = "../autoscaling-ecs"
  alb_name              = module.load_balance.lb_name
  app_name              = var.app_name
  create_resource       = var.create_resource
  max_capacity          = var.max_capacity
  min_capacity          = var.min_capacity
  tags                  = var.tags

}

data "template_file" "container_definitions" {
  template = "${file("${path.module}/container_definitions.json")}"
  vars = {
    image          = var.image
    #app_port = var.app_port
    container_name = "${var.environment}-${var.product}-container"
    region         = var.region
    log_group      = module.log_group.cloudwatch_loggroup_name
    env            = var.environment
    command        = jsonencode(var.command)
    java_opts      = var.java_opts
    app_name       = var.java_app_name
  }
}

resource "aws_ecs_cluster" "ecs_cluster" {
  name  = "${var.app_name}-ecs-cluster"
  tags  = merge(var.tags)
}

resource "aws_ecs_task_definition" "ecs_task_definition" {
  family                   = "${var.app_name}-ecs-task"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.task_cpu
  memory                   = var.task_mem
  network_mode             = "awsvpc"
  task_role_arn            = var.ecs_role_arn
  execution_role_arn       = var.ecs_role_arn
  container_definitions    = data.template_file.container_definitions.rendered

  tags  = merge(var.tags)
}

//https://docs.aws.amazon.com/AmazonECS/latest/developerguide/service_definition_parameters.html
resource "aws_ecs_service" "ecs_service" {
  name                                  =  var.app_name
  cluster                               =  aws_ecs_cluster.ecs_cluster.arn
  task_definition                       =  "${aws_ecs_task_definition.ecs_task_definition.family}:${aws_ecs_task_definition.ecs_task_definition.revision}"
  launch_type                           =  "FARGATE"
  desired_count                         =  var.desired_count
  deployment_maximum_percent            =  200
  deployment_minimum_healthy_percent    =  60
  health_check_grace_period_seconds     =  30
  force_new_deployment                  =  true
  network_configuration {
    subnets                             =  var.subnets_ids
    security_groups                     =  module.sec_groups.security_groups_ids
    assign_public_ip                    =  true
  }
  load_balancer {
    target_group_arn                    =  module.load_balance.lb_taiget_group_arn
    container_name                      =  "${var.environment}-${var.product}-container"
    container_port                      =  80
  }
  deployment_controller {
    type                                =  "ECS"
  }
  lifecycle {
    ignore_changes                      =  [desired_count]
  }

  enable_ecs_managed_tags               = true
  propagate_tags                        =  "TASK_DEFINITION"
  tags                                  =  merge(var.tags)
}