family: ts00804-prd-app-eqapi-ongoing-dump-datalake1
executionRoleArn: arn:aws:iam::<+variable.AWS_PROD_ACCOUNT_ID>:role/ts00804-prd-us-east-1-app-ongoing-dump-role
taskRoleArn: arn:aws:iam::<+variable.AWS_PROD_ACCOUNT_ID>:role/ts00804-prd-us-east-1-app-ongoing-dump-role
containerDefinitions:
  - name: ts00804-prd-app-eqapi-ongoing-dump-datalake1-container
    image: <+variable.AWS_PROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/eqapi/api-v2:<+serviceVariables.ImageTag>
    cpu: 2048
    essential: true
    command: 
      - "--control=datalake"
      - "--spring.profiles.active=prd"
      - "--minDlLoadDates=default"
    environment:
      - name: JAVA_OPTS
        value: -XX:MaxRAMPercentage=80.0 -XX:MinRAMPercentage=40.0 -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC
      - name: APP_NAME
        value: ongoing
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /aws/ecs/prd-eqapi-ongoing-dump
        awslogs-region: <+infra.region>
        awslogs-stream-prefix: ecs
cpu: '2048'
memory: '15800'
requiresCompatibilities:
  - EC2
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>