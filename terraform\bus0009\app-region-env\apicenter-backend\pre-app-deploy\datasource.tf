data "aws_lb" "apicenter_lb" {
  name = local.alb_map[var.environment]
}

data "aws_lb_listener" "apicenter_lb_listener" {
  load_balancer_arn = data.aws_lb.apicenter_lb.arn
  port              = 443
}

data "aws_kinesis_stream" "stream" {
  name  = var.kinesis_stream_name
}

data "aws_iam_role" "log_role" {
  name = "CWLtoKinesis"
}

data "aws_acm_certificate" "ms_certificate" {
  count  = var.environment == "prod" ? 1 : 0
  domain = "apicentersupport.morningstar.com"
  statuses = ["ISSUED"]
}