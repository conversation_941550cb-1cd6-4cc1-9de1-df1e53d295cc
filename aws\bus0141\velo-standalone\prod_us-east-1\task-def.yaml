family: velo-standalone-task-definition-prod
executionRoleArn: arn:aws:iam::<+variable.AWS_PROD_ACCOUNT_ID>:role/velo-stand-alone-prod
taskRoleArn: arn:aws:iam::<+variable.AWS_PROD_ACCOUNT_ID>:role/velo-stand-alone-prod
containerDefinitions:
  - name: velo-standalone-container-prod
    image: <+variable.AWS_PROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/velo-standalone-ecr:<+serviceVariables.ImageTag>
    cpu: 1024
    memory: 2048
    portMappings:
      - hostPort: 80
        protocol: tcp
        containerPort: 80
    essential: true
    environment:
      - name: ACTIVE_ENV
        value: prod-us-east-1
      - name: aws_region
        value: us-east-1
      - name: S3_BUCKET_URL
        value: ui-resources-prod-us-east-1.s3.amazonaws.com
      - name: S3_DNS_SERVERS
        value: morningstar.com ***************
      - name: datasource_session_variable
        value: <+serviceVariables.DATASOURCE_SESSION_VARIABLE>
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /ecs/velo-standalone-prod
        awslogs-region: us-east-1
        awslogs-stream-prefix: ecs
    secrets:
      - name: SPRING_DATASOURCE_WRITE_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_PROD_ACCOUNT_ID>:parameter/VELO_WEBSITE/DB_PASSWORD"
      - name: SPRING_DATASOURCE_READ_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_PROD_ACCOUNT_ID>:parameter/VELO_WEBSITE/DB_PASSWORD"
      - name: APIGEE_CLIENTID
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_PROD_ACCOUNT_ID>:parameter/VELO/APIGEE_CLIENT_ID"
      - name: APIGEE_CLIENTSECRET
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_PROD_ACCOUNT_ID>:parameter/VELO/APIGEE_CLIENT_SECRET"
      - name: VELOADMIN_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_PROD_ACCOUNT_ID>:parameter/VELO_WEBSITE/ADMIN_PASSWORD"

cpu: '1024'
memory: '2048'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>