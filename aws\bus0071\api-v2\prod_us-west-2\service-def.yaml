launchType: FARGATE
serviceName: ts00804-prd-eqapi-restful-api
desiredCount: 0
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-d3a2a2ac # private_app
      - sg-93a1a1ec # private_web
    subnets:
      - subnet-ff70a886 
      - subnet-1ed75044
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 50

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: prd-restful-api-container
    containerPort: 80