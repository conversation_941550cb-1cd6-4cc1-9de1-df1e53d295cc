launchType: FARGATE
serviceName: async-api-service-prod
desiredCount: 2
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0e7ef1010d6acddf8  #private_web
      - sg-0d788a43acb654693  #private_app
      - sg-078ee4651c9f093eb  #private_db
      - sg-05c2bbd42b8377962  #private_internal_email_morningstar
      - sg-0dc4883f59ecd3226  #private_mongo
    subnets:
      - subnet-007aa27f3f50932a2
      - subnet-08f032a54afd01bcc
      - subnet-0adafb2337a7adb7a
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 100
tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION