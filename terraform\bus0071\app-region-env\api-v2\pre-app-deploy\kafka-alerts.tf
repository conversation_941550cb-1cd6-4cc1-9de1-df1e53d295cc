
module "kafka_sns_topic" {
  source              = "./../../../modules/sns-topic"
  environment         = var.environmentForTagging
  region              = var.region
  tsid                = var.tsid
  function            = var.function
  sns_topic_name      = "ongoing-kafka-alert"
  sns_topic_recepient = var.sns_topic_recepient
  create_resource     = var.create_resource
  tags                = var.tags
}

#group 1
module "gedf-kafka-lag-monitor_market_capital" {
  source = "./../../../modules/kafka-lag-monitor"
  env            = var.environmentForTagging
  tid            = var.tsid
  service_name   = "EQAPI"
  consumer_group = "EQAPI_ged_equitydata_market_capital"
  workflow       = "ged_equitydata_market_capital"
  num_periods    = "1"
  period         = "300"
  max_lag        = "20000"
  aws_region     = var.region
  sns_topic      = module.kafka_sns_topic.sns_topic_arn
  runbook        = local.message
}


module "gedf-kafka-lag-monitor_market_return" {
  source = "./../../../modules/kafka-lag-monitor"
  env            = var.environmentForTagging
  tid            = var.tsid
  service_name   = "EQAPI"
  consumer_group = "EQAPI_ged_equitydata_market_return"
  workflow       = "ged_equitydata_market_return"
  num_periods    = "1"
  period         = "300"
  max_lag        = "20000"
  aws_region     = var.region
  sns_topic      = module.kafka_sns_topic.sns_topic_arn
  runbook        = local.message
}



#gropu 2
module "gedf-kafka-lag-monitor" {
  source = "./../../../modules/kafka-lag-monitor"
  env            = var.environmentForTagging
  tid            = var.tsid
  service_name   = "EQAPI"
  consumer_group = "EQAPI_ged_equitydata_income_statement"
  workflow       = "ged_equitydata_income_statement"
  num_periods    = "1"
  period         = "300"
  max_lag        = "500"
  aws_region     = var.region
  sns_topic      = module.kafka_sns_topic.sns_topic_arn
  runbook        = local.message
}

module "gedf-kafka-lag-monitor_cash_dividend" {
  source = "./../../../modules/kafka-lag-monitor"
  env            = var.environmentForTagging
  tid            = var.tsid
  service_name   = "EQAPI"
  consumer_group = "EQAPI_ged_equitydata_cash_dividend"
  workflow       = "ged_equitydata_cash_dividend"
  num_periods    = "1"
  period         = "300"
  max_lag        = "3500"
  aws_region     = var.region
  sns_topic      = module.kafka_sns_topic.sns_topic_arn
  runbook        = local.message
}

#group 3
module "gedf-kafka-lag-monitor_balance_sheet" {
  source = "./../../../modules/kafka-lag-monitor"
  env            = var.environmentForTagging
  tid            = var.tsid
  service_name   = "EQAPI"
  consumer_group = "EQAPI_ged_equitydata_balance_sheet"
  workflow       = "ged_equitydata_balance_sheet"
  num_periods    = "1"
  period         = "300"
  max_lag        = "500"
  aws_region     = var.region
  sns_topic      = module.kafka_sns_topic.sns_topic_arn
  runbook        = local.message
}

module "gedf-kafka-lag-monitor_profitablity" {
  source = "./../../../modules/kafka-lag-monitor"
  env            = var.environmentForTagging
  tid            = var.tsid
  service_name   = "EQAPI"
  consumer_group = "EQAPI_ged_equitydata_profitability"
  workflow       = "ged_equitydata_profitability"
  num_periods    = "1"
  period         = "300"
  max_lag        = "3500"
  aws_region     = var.region
  sns_topic      = module.kafka_sns_topic.sns_topic_arn
  runbook        = local.message
}
