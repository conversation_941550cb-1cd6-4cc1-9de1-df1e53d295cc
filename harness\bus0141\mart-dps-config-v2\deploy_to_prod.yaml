name: Copy Artifacts from versioned to prod bucket
items:
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-prod-us-east-1
      key: mart-dps-config/{{sourceVersion}}/dps-config.zip
      expandZip: true
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: mart-data-prod
        key: config/
      - accountId: "************"
        region: us-west-2
        bucket: mart-data-prod-dr
        key: config/
      - accountId: "************"
        region: eu-west-1
        bucket: mart-data-prod-eu
        key: config/
      # Blue Environment for B/G Deployment
      - accountId: "************"
        region: us-east-1
        bucket: mart-data-prod
        key: blue-env-config/
      - accountId: "************"
        region: us-west-2
        bucket: mart-data-prod-dr
        key: blue-env-config/
      - accountId: "************"
        region: eu-west-1
        bucket: mart-data-prod-eu
        key: blue-env-config/

#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-prod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-prod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/xoi_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/xoi_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/xoi_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/xoi_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-prod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/lh_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/lh_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/lh_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/lh_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-prod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/athena_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/athena_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/athena_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/athena_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-prod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/ds_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/ds_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/ds_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/ds_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-prod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/fi_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/fi_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/fi_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/fi_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-prod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/data-group.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/data-group.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/data-group.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/data-group.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-prod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/mapping.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/mapping.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/mapping.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/mapping.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-prod-us-east-1
#      key: mart-dps-config/rdb/{{sourceVersion}}/rdb_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/rdb/rdb_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/rdb/rdb_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/rdb/rdb_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-prod-us-east-1
#      key: mart-dps-config/rdb/{{sourceVersion}}/rdb_direct.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/rdb/rdb_direct.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/rdb/rdb_direct.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/rdb/rdb_direct.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-prod-us-east-1
#      key: mart-dps-config/view/{{sourceVersion}}/datapoints_view.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/view/datapoints_view.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/view/datapoints_view.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/view/datapoints_view.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-prod-us-east-1
#      key: mart-dps-config/view/{{sourceVersion}}/direct_datapoints_view.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/view/direct_datapoints_view.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/view/direct_datapoints_view.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/view/direct_datapoints_view.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-prod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/datapoint_whitelist.txt
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/datapoint_whitelist.txt
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/datapoint_whitelist.txt
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/datapoint_whitelist.txt
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-prod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/equity_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/equity_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/equity_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/equity_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-prod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/code_mappings.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/code_mappings.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/code_mappings.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/code_mappings.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-prod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/calculations.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/calculations.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/calculations.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/calculations.xml
