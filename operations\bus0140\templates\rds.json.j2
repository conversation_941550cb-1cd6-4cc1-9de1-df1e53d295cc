{
  "DBInstanceIdentifier": "instance",
  "DBSnapshotIdentifier": "snapshot",
  "PubliclyAccessible": false,
  "DBInstanceClass": "{{ rds_instance_class }}",
  "DBParameterGroupName": "postgres13-custom",
  "MultiAZ": false,
  "DBSubnetGroupName": "private_db_az_subnets",
  "Tags": [
    {% for key, value in tags.items() %}
    {
      "Key": "{{ key }}",
      "Value": "{{ value }}"
    }
    {% if not loop.last %},
    {% endif %}
    {% endfor %}
  ],
  "VpcSecurityGroupIds": {{vpc_security_group_ids|tojson()}}
}