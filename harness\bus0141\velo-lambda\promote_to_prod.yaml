name: Copy Artifacts from non-prod to prod account
items:
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: velo-lambda/app-async-universe-list/{{sourceVersion}}/app.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: velo-lambda/app-async-universe-list/{{targetVersion}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-prod-us-west-2
        key: velo-lambda/app-async-universe-list/{{targetVersion}}/app.jar
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: velo-lambda/app-feed-dfd/{{sourceVersion}}/app.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: velo-lambda/app-feed-dfd/{{targetVersion}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-prod-us-west-2
        key: velo-lambda/app-feed-dfd/{{targetVersion}}/app.jar
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: velo-lambda/app-feed-fdo/{{sourceVersion}}/app.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: velo-lambda/app-feed-fdo/{{targetVersion}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-prod-us-west-2
        key: velo-lambda/app-feed-fdo/{{targetVersion}}/app.jar
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: velo-lambda/app-feed-message/{{sourceVersion}}/app.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: velo-lambda/app-feed-message/{{targetVersion}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-prod-us-west-2
        key: velo-lambda/app-feed-message/{{targetVersion}}/app.jar
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: velo-lambda/app-feed-notification-handler/{{sourceVersion}}/app.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: velo-lambda/app-feed-notification-handler/{{targetVersion}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-prod-us-west-2
        key: velo-lambda/app-feed-notification-handler/{{targetVersion}}/app.jar
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: velo-lambda/app-notebook-sns-listener/{{sourceVersion}}/app.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: velo-lambda/app-notebook-sns-listener/{{targetVersion}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-prod-us-west-2
        key: velo-lambda/app-notebook-sns-listener/{{targetVersion}}/app.jar
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: velo-lambda/disable-feed-expired-licenses/{{sourceVersion}}/app.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: velo-lambda/disable-feed-expired-licenses/{{targetVersion}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-prod-us-west-2
        key: velo-lambda/disable-feed-expired-licenses/{{targetVersion}}/app.jar
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: velo-lambda/standard-feed-sns-notification/{{sourceVersion}}/app.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: velo-lambda/standard-feed-sns-notification/{{targetVersion}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-prod-us-west-2
        key: velo-lambda/standard-feed-sns-notification/{{targetVersion}}/app.jar
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: velo-lambda/sync-index-series-to-entitlement/{{sourceVersion}}/app.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: velo-lambda/sync-index-series-to-entitlement/{{targetVersion}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-prod-us-west-2
        key: velo-lambda/sync-index-series-to-entitlement/{{targetVersion}}/app.jar
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: velo-lambda/equity-delta-sns-listener/{{sourceVersion}}/app.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: velo-lambda/equity-delta-sns-listener/{{targetVersion}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-prod-us-west-2
        key: velo-lambda/equity-delta-sns-listener/{{targetVersion}}/app.jar