

module "log_group" {
  source = "./../../../modules/cloudwatch-logs"
  log_group = var.log_group
  tags = merge(var.tags)
  environment = var.environment
  region = var.region
}



module "gedf-ongoing-error-log-monitor" {
  source                                 = "./../../../modules/cloudwatch-log-metric-filter"
  log_metric_filter_name                 = "DeliveryErrors"
  log_metric_filter_pattern              = "?ERROR ?Error ?Exception ?EXCEPTION"
  log_group_name                         = module.log_group.cloudwatch_loggroup_name
  log_filter_metric_transformation_name  = "${var.tsid}-${var.environmentForTagging}-${var.function}-ongoing-dump-delivery-error"
  log_filter_metric_transformation_value = "1"
  metric_alarm_name                      = "${var.tsid}-${var.environmentForTagging}-${var.function}-ongoing-dump-Build-Occur-Error"
  comparison_operator                    = "GreaterThanOrEqualToThreshold"
  evaluation_periods                     = 1
  period                                 = "60"
  statistic                              = "Sum"
  threshold                              = 1
  actions                                = module.sns_topic.sns_topic_arn
  create_resource                        = var.create_resource
  tags                                   = var.tags
}