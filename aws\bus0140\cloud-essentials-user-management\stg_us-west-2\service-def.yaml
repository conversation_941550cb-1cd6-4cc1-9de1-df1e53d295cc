launchType: FARGATE
serviceName: essentials-user-service-stg
desiredCount: 0
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0cb0b481497065818 # private_app
      - sg-03aae3fda738ccd3f # private_web
      - sg-0a719818294fd3f0b # private_db
    subnets:
      - subnet-084b8c666e71a339f
      - subnet-0cfbd3bd59d7c2cad
      - subnet-03d560dd0e5253099
    assignPublicIp: DISABLED
deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 50
healthCheckGracePeriodSeconds : 300
schedulingStrategy : REPLICA
loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: essentials-user-container-stg
    containerPort: 8080
tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION
