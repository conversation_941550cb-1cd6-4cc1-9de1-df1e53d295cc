region="us-east-1"
role_to_assume="arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment="stg"
environmentForTagging="stg"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

account_id      = "************"
app_env         = "stg"
s3_bucket       = "stg-equity-mart"
subnets_ids     = ["subnet-d20a5cff","subnet-9a91dcc1"]

log_group       = "/aws/ecs/stg-eqapi-ongoing-dump"
instance_type   = "t3.large"
instance_size   = 4
ec2_keypair     = "keypair_gedf_non_prod_test"
task_cpu        = 2048
task_memory     = 7873
s3_task_memory  = 7168
java_opts       = "-XX:MaxRAMPercentage=80.0 -XX:MinRAMPercentage=40.0 -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC"
create_resource = 1

ami_id                        =  "ami-0bee5171eb1f564e1"
java_opts_batch               =  "-DOperationLogGroup=/aws/batch/stg-operations -DOperationLogStream=stg-eqapi-onetime-dump -Xmx24G -Xms24G -XX:+UseG1GC"
batch_instance_type           =  ["m5.2xlarge"]
batch_instance_volume_type    =  "io1"
batch_instance_volume_size    =  2000
batch_instance_volume_iops    =  10000

redis_node_type               = "cache.t3.medium"
redis_node_num                = 1
redis_shard_num               = 1
redis_replicas_per_shard      = 0
redis_engine_version          = "5.0.0"
redis_parameter_group         = "default.redis5.0.cluster.on"

es_instance_type              = "t3.medium.elasticsearch"
es_instance_size              = 1
es_volume_size                = 30

create_resource_kafka1 = 1
create_resource_kafka2 = 1
create_resource_kafka3 = 1
create_resource_datalake1 = 1

resume_offset = "default"
minDlLoadDates = "default"

access_log_bucket          = "mstar-s3-datasvc-non-prod-splunk-us-east-1"
web_java_opts                  = "-Dnewrelic.environment=staging -javaagent:/usr/local/newrelic/newrelic.jar -XX:MaxRAMPercentage=80.0 -XX:MinRAMPercentage=40.0 -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC"
desired_count              = 1
min_capacity               = 0
max_capacity               = 1
certificate_domain         = "*.datf01ae.easn.morningstar.com"
#https://docs.aws.amazon.com/zh_cn/AmazonECS/latest/developerguide/task_definition_parameters.html#task_size
web_task_cpu                   = 2048
web_task_mem                   = 4096
r53_routing_live_weightage = 1
alb_healthy_threshold      = 2
alb_unhealthy_threshold    = 2
alb_health_check_timeout   = 5
alb_health_check_path      = "/"
alb_health_check_interval  = 10


group1_workflows = [
  "ged_equitydata_market_capital",
  "ged_equitydata_ecs_component_data",
  "ged_equitydata_ecs_outstanding_data",
  "ged_equitydata_capital_calculations_equity_component_level",
  "ged_equitydata_stock_options_restricted_stock_units_and_warrants_company_level",
  "ged_equitydata_stock_options_restricted_stock_units_and_warrants_plan_level",
  "ged_equitydata_stock_options_restricted_stock_units_and_warrants_tranche_level",
  "ged_equitydata_diluted_market_capital",
  "ged_equitydata_diluted_enterprise_value_calculations",
  "ged_equitydata_trailing_return",
  "ged_equitydata_market_return",
  "ged_equitydata_spin_values",
  "ged_equitydata_daily_standard_deviation",
  "ged_equitydata_total_shares_outstanding",
  "ged_equitydata_price_multiples",
  "ged_equitydata_calculation_fiscal_period_dividend_metrics",
  "ged_equitydata_margins"
]
group2_workflows = [
  "ged_equitydata_income_statement_cpms",
  "ged_equitydata_share_class_information",
  "ged_equitydata_earning_report_cpms",
  "ged_equitydata_debt_maturity_schedule",
  "ged_equitydata_income_statement",
  "ged_equitydata_cash_flow",
  "ged_equitydata_earning_report",
  "ged_equitydata_spin_off",
  "ged_equitydata_stock_split",
  "ged_equitydata_rights_issue",
  "ged_equitydata_cash_dividend",
  "ged_equitydata_capital_repayment",
  "ged_equitydata_income_statement_calculations_credit_analysis",
  "ged_equitydata_balance_sheet_calculations_credit_analysis",
  "ged_equitydata_operating_metrics_industry_ratio",
  "ged_equitydata_operating_metrics_income_statement",
  "ged_equitydata_operating_metrics_balance_sheet",
  "ged_equitydata_balance_sheet_supplemental",
  "ged_equitydata_income_statement_supplemental",
  "ged_equitydata_financial_ratio_supplemental",
  "ged_equitydata_segment_data_supplemental",
  "ged_equitydata_calculated_metrics",
  "ged_equitydata_calculated_ratio",
  "ged_equitydata_cash_flow_calculations",
  "ged_equitydata_income_statement_calculations",
  "ged_equitydata_cash_flow_calculations_per_share",
  "ged_equitydata_enterprise_value_calculations",
  "ged_equitydata_efficiency_ratios",
  "ged_equitydata_leverage"
]
group3_workflows = [
  "ged_equitydata_daily_dividend_residual_risk_and_return_sensitivity",
  "ged_equitydata_daily_non_dividend_residual_risk_and_return_sensitivity",
  "ged_equitydata_balance_sheet",
  "ged_equitydata_monthly_standard_deviation",
  "ged_equitydata_dividend_residual_risk_and_return_sensitivity",
  "ged_equitydata_non_dividend_residual_risk_and_return_sensitivity",
  "ged_equitydata_company_information",
  "ged_equitydata_ecs_event_data",
  "ged_equitydata_ecs_profiling_data",
  "ged_equitydata_ecs_underlying_component_data",
  "ged_equitydata_regulatory_data_for_us_bank_holding_companies",
  "ged_equitydata_net_asset_value",
  "ged_equitydata_net_asset_value_per_share",
  "ged_equitydata_bank_reserve_ratio_duration",
  "ged_equitydata_bank_reserve_ratio_spot",
  "ged_equitydata_fund_from_operations",
  "ged_equitydata_fund_from_operations_per_share",
  "ged_equitydata_segments_enhanced",
  "ged_equitydata_balance_sheet_calculations",
  "ged_equitydata_profitability",
  "ged_equitydata_balance_sheet_calculations_per_share",
  "ged_equitydata_income_statement_calculations_per_share",
  "ged_equitydata_profitability_per_share",
  "ged_equitydata_balance_sheet_reported_averages",
  "ged_equitydata_price_yields",
  "ged_equitydata_dividend_metrics"
]
#tags
vpc_tag         = "datasvc-non-prod"
tags = {
  ENVIRONMENT    = "stg"
  ORG            = "dataservice"
  MANAGED        = "terraform"
  PID            = "PID0497"
  SERVICE        = "Equity API V2"
  TID            = "EQAPI"
  SERVICEID      = "ts00804"
  BUSID          = "bus0071"
  FUNCTION       = "app"
  Name           = "ts00804-stg-eqapi-ongoing-dump"
}
batch_tags = {
  ENVIRONMENT    = "stg"
  ORG            = "dataservice"
  MANAGED        = "terraform"
  PID            = "PID0497"
  SERVICE        = "Equity API V2"
  TID            = "EQAPI"
  SERVICEID      = "ts00804"
  BUSID          = "bus0071"
  FUNCTION       = "app"
  Name           = "ts00804-stg-eqapi-onetime-dump"
}
web_tags =  {
  ENVIRONMENT              =  "stg"
  ORG                      =  "dataservice"
  MANAGED                  =  "terraform"
  PID                      =  "PID0497"
  SERVICE                  =  "Equity API V2"
  TID                      =  "EQAPI"
  SERVICEID                =  "ts00804"
  BUSID                    =  "bus0071"
  FUNCTION                 =  "web"
}
esg_tags=[
  {
    key = "Name"
    value= "ts00804-stg-eqapi-ongoing-dump"
    propagate_at_launch = true
  },
  {
    key                 = "BUSID"
    value               = "bus0071"
    propagate_at_launch = true
  },
  {
    key                 = "ORG"
    value               = "dataservice"
    propagate_at_launch = true
  },
  {
    key                 = "SERVICEID"
    value               = "ts00804"
    propagate_at_launch = true
  },
  {
    key                 = "ENVIRONMENT"
    value               = "stg"
    propagate_at_launch = true
  },
  {
    key                 = "PID"
    value               = "PID0497"
    propagate_at_launch = true
  },
  {
    key                 = "SERVICE"
    value               = "Equity API V2"
    propagate_at_launch = true
  },
  {
    key                 = "MANAGED"
    value               = "terraform"
    propagate_at_launch = true
  },
  {
    key                 = "TID"
    value               = "EQAPI"
    propagate_at_launch = true
  },
  {
    key                 = "FUNCTION"
    value               = "app"
    propagate_at_launch = true
  }
]

#sns
sns_topic_name = "eqapi-ongoing-dump-operations-notification"
sns_topic_recepient = "<EMAIL>"

