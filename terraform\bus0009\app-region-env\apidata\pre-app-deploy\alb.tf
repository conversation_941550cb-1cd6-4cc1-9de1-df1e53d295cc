resource "aws_lb_target_group" "fund_api_lb_target_group" {
  name                          = "${var.application_name}-target-group-${var.environment}"
  port                          = var.host_port
  protocol                      = "HTTP"
  target_type                   = "ip"
  vpc_id                        = var.vpc_id
  deregistration_delay          = var.deregistration_delay
  load_balancing_algorithm_type = "least_outstanding_requests"
  tags                          = local.tags

  health_check {
    path              = "/v2/health"
    port              = var.host_port
    healthy_threshold = 3
    interval          = 30
    timeout           = 25
  }

  lifecycle {
    create_before_destroy = true
    ignore_changes        = [name]
  }

  depends_on = [data.aws_lb.fund_api_lb]
}

resource "aws_lb_listener_certificate" "fund_api_listener_certificate" {
  listener_arn    = data.aws_lb_listener.fund_api_lb_listener.arn
  certificate_arn = var.listener_certificate_arn
}

resource "aws_lb_listener_rule" "apicenter_lb_https_listener_rule" {
  listener_arn = data.aws_lb_listener.fund_api_lb_listener.arn
  priority     = var.listener_priority

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.fund_api_lb_target_group.id
  }
  condition {
    path_pattern {
      values = ["/v2/*", "/oauth/*"]
    }
  }
  condition {
    host_header {
      values = concat(var.host_header, [data.aws_lb.fund_api_lb.dns_name])
    }
  }
  depends_on = [aws_lb_target_group.fund_api_lb_target_group]
}