variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

variable "application_name" {
  type = string
}

variable "max_count" {
  type = number
}

variable "min_count" {
  type = number
}

variable "peak_max_count" {
  type = number
}

variable "peak_min_count" {
  type = number
}

variable "ecs_task_schedule_up" {
  type = string
}

variable "ecs_task_schedule_down" {
  type = string
}

variable "cloudwatch_evaluation_period" {
  type = number
}

variable "cloudwatch_period" {
  type = number
}

variable "scale_out_threshold" {
  type = number
}

variable "scale_in_threshold" {
  type = number
}

variable "enable_vo_alert" {
  type    = bool
  default = false
}

locals {
  mart_role_map = {
    dev = "mart-role-dev",
    uat = "mart-role-prod",
    prod = "mart-role-prod"
  }

  alb_map = {
    dev = "fundapi-alb-nonp",
    uat = "fundapi-alb-prod",
    prod = "fundapi-alb-prod"
  }

  tags = {
    SERVICEID   = "ts00057",
    ENVIRONMENT = var.environmentForTagging,
    PID         = "PID0029",
    TID         = "DATAAC",
    MANAGED     = "terraform"
  }
}