launchType: FARGATE
serviceName: ts00804-stg-eqapi-restful-api
desiredCount: 1
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-05b55c7e # private_app
      - sg-43b45d38 # private_web
    subnets:
      - subnet-3de7815a
      - subnet-3d6bd374
      - subnet-c9627091
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 50

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: stg-restful-api-container
    containerPort: 80