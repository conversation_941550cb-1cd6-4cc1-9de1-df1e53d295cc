family: essentials-user-task-stg
executionRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/essentials-role-stg
taskRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/essentials-role-stg
containerDefinitions:
  - name: essentials-user-container-stg
    image: <+variable.AWS_NONPROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/cloud-essentials-user-management-ecr:<+serviceVariables.ImageTag>
    cpu: 256
    portMappings:
      - hostPort: 8080
        protocol: tcp
        containerPort: 8080
    essential: true
    environment:
      - name: ACTIVE_ENV
        value: stg
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /ecs/essentials-user-task-stg
        awslogs-region: us-east-1
        awslogs-stream-prefix: ecs
cpu: '256'
memory: '512'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: NAME
    value: essentials-user-management-stg
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
  - key: PID
    value: PID0029
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
