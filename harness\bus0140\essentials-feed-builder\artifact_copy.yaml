name: Copy Artifacts to S3
items:
  - sourceFile: data-readiness-check/target/data-readiness-check.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: data-readiness-check/{{version}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-nonprod-us-west-2
        key: data-readiness-check/{{version}}/app.jar
  - sourceFile: delivery/target/delivery.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: essentials-feed-builder/delivery/{{version}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-nonprod-us-west-2
        key: essentials-feed-builder/delivery/{{version}}/app.jar