family: essentials-image-loader-task-prod
executionRoleArn: arn:aws:iam::<+variable.AWS_PROD_ACCOUNT_ID>:role/essentials-role-prod
taskRoleArn: arn:aws:iam::<+variable.AWS_PROD_ACCOUNT_ID>:role/essentials-role-prod
containerDefinitions:
  - name: essentials-image-loader-container-prod
    image: <+variable.AWS_PROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/cloud-essentials-image-loader-ecr:<+serviceVariables.ImageTag>
    cpu: 256
    portMappings:
      - hostPort: 8080
        protocol: tcp
        containerPort: 8080
    essential: true
    environment:
      - name: ACTIVE_ENV
        value: prod-dr
      - name: AWS_REGION
        value: us-west-2
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /ecs/essentials-image-loader-task-prod
        awslogs-region: us-west-2
        awslogs-stream-prefix: ecs
cpu: '256'
memory: '512'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: NAME
    value: essentials-image-loader-management-prod
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
  - key: PID
    value: PID0029
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
