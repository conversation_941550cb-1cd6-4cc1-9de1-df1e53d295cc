family: mart-monitor-definition-dev
executionRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/mart-role-dev
taskRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/mart-role-dev
containerDefinitions:
  - name: mart-monitor-container-dev
    image: <+variable.AWS_NONPROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/mart-monitor-ecr:<+serviceVariables.ImageTag>
    cpu: 512
    memory: 1024
    portMappings:
      - hostPort: 8080
        protocol: tcp
        containerPort: 8080
    essential: true
    environment:
      - name: ACTIVE_ENV
        value: dev
      - name: datasource_session_variable
        value: <+serviceVariables.DATASOURCE_SESSION_VARIABLE>
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /ecs/mart-monitor-dev
        awslogs-region: us-east-1
        awslogs-stream-prefix: ecs
    secrets:
      - name: SPRING_DATASOURCE_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/MART_MONITOR/DB_PASSWORD"
      - name: UIM_ADMIN_APIKEY
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/VELO/UIM_ADMIN_APIKEY"
      - name: APIGEE_CLIENTID
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/VELO/APIGEE_CLIENT_ID"
      - name: APIGEE_CLIENTSECRET
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/VELO/APIGEE_CLIENT_SECRET"
      - name: UIM_VELOADMIN_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/VELO_WEBSITE/ADMIN_PASSWORD"
      - name: AWS_IAM_SENDGRIDAPIKEY
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/velo_sendgrid_apikey_dev"

cpu: '512'
memory: '1024'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>