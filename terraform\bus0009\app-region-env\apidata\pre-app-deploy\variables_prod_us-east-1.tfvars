region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "prod"
environmentForTagging = "prd"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

vpc_id               = "vpc-0d8285c959886d2c0"
deregistration_delay = 300
listener_certificate_arn = "arn:aws:acm:us-east-1:************:certificate/57b2b9bc-c4fd-48d7-a942-843d700ba1a1"  # *.dat688b3.eas.morningstar.com
listener_priority    = 110
host_header = ["fundapi-prod.dat688b3.eas.morningstar.com", "api.morningstar.com"]
kinesis_stream_name  = "mstar-kinesis-dataac-prod-splunk-us-east-1"
log_retention_days   = 3