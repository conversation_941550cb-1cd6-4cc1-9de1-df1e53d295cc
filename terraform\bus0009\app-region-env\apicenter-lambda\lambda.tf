resource "aws_lambda_function" "apicenter_dfd_generator" {
  function_name                  = "apicenter-dfd-generator-${var.environment}"
  description                    = "generate apicenter dfd"
  s3_bucket                      = local.lambda_s3_bucket
  s3_key                         = "apicenter-dfd/${var.release_version}/app.jar"
  role                           = data.aws_iam_role.mart-role.arn
  runtime                        = "java17"
  handler                        = "com.morningstar.apicenter.dfd.function.DFDLambda::handleMessage"
  timeout                        = "600"
  memory_size                    = "1024"
  reserved_concurrent_executions = var.reserved_lambda_concurrent_executions

  vpc_config {
    subnet_ids = [
      module.region_data.private_subnet_1_id[0],
      module.region_data.private_subnet_2_id[0],
      module.region_data.private_subnet_3_id[0]
    ]

    security_group_ids = [
      module.region_data.security_group_private_app[0],
      module.region_data.security_group_private_db[0],
      module.region_data.security_group_private_web[0]
    ]
  }

  tags = local.tags

  environment {
    variables = {
      active_env = var.application_profile
    }
  }
}

resource "aws_lambda_event_source_mapping" "feed_message_event_mapping" {
  depends_on = [aws_sqs_queue.apicenter_dfd_generator_queue]
  event_source_arn = aws_sqs_queue.apicenter_dfd_generator_queue.arn
  enabled          = true
  batch_size       = 1
  function_name    = aws_lambda_function.apicenter_dfd_generator.arn
}

resource "aws_cloudwatch_log_group" "lambda-apicenter-dfd-generator-log" {
  name              = "/aws/lambda/apicenter-dfd-generator-${var.environment}"
  retention_in_days = var.log_retention_days

  tags = local.tags
}

resource "aws_cloudwatch_log_subscription_filter" "apicenter-lambda-dfd-generator-log-subscription" {
  name            = "apicenter-dfd-generator-log-subscription-${var.environment}"
  log_group_name  = aws_cloudwatch_log_group.lambda-apicenter-dfd-generator-log.name
  filter_pattern  = ""
  destination_arn = data.aws_kinesis_stream.stream.arn
  role_arn        = data.aws_iam_role.log_role.arn
  depends_on = [aws_cloudwatch_log_group.lambda-apicenter-dfd-generator-log]
}

resource "aws_lambda_function" "async_api_worker" {
  function_name                  = "apicenter-async-api-worker-${var.environment}"
  description                    = "process API Center Async API requests"
  s3_bucket                      = local.lambda_s3_bucket
  s3_key                         = "apicenter-async-api/${var.release_version}/app.jar"
  role                           = data.aws_iam_role.mart-role.arn
  runtime                        = "java17"
  handler                        = "com.morningstar.async.function.MessageLambda::handleMessages"
  timeout                        = "600"
  memory_size                    = "4096"
  reserved_concurrent_executions = var.deploy_max

  vpc_config {
    subnet_ids = [
      module.region_data.private_subnet_1_id[0],
      module.region_data.private_subnet_2_id[0],
      module.region_data.private_subnet_3_id[0]
    ]

    security_group_ids = [
      module.region_data.security_group_private_app[0],
      module.region_data.security_group_private_db[0],
      module.region_data.security_group_private_web[0]
    ]
  }
  file_system_config {
    # EFS file system access point ARN
    arn = aws_efs_access_point.access_point_for_async_lambda.arn

    # Local mount path inside the lambda function. Must start with '/mnt/'.
    local_mount_path = "/mnt/data"
  }

  depends_on = [
    aws_efs_mount_target.mount_target_1,
    aws_efs_mount_target.mount_target_2,
    aws_efs_mount_target.mount_target_3,
  ]

  tags = local.tags

  environment {
    variables = {
      active_env = var.application_profile
    }
  }
}


# EFS file system
resource "aws_efs_file_system" "async_lambda_volumn" {
  tags = local.tags
}

# Mount targets connect the file system to the subnet
resource "aws_efs_mount_target" "mount_target_1" {
  file_system_id = aws_efs_file_system.async_lambda_volumn.id
  subnet_id      = module.region_data.private_subnet_1_id[0]

  security_groups = [
    module.region_data.security_group_private_app[0],
    module.region_data.security_group_private_web[0]
  ]
}

resource "aws_efs_mount_target" "mount_target_2" {
  file_system_id = aws_efs_file_system.async_lambda_volumn.id
  subnet_id      = module.region_data.private_subnet_2_id[0]

  security_groups = [
    module.region_data.security_group_private_app[0],
    module.region_data.security_group_private_web[0]
  ]
}

resource "aws_efs_mount_target" "mount_target_3" {
  file_system_id = aws_efs_file_system.async_lambda_volumn.id
  subnet_id      = module.region_data.private_subnet_3_id[0]

  security_groups = [
    module.region_data.security_group_private_app[0],
    module.region_data.security_group_private_web[0]
  ]
}

# EFS access point used by lambda file system
resource "aws_efs_access_point" "access_point_for_async_lambda" {
  file_system_id = aws_efs_file_system.async_lambda_volumn.id

  root_directory {
    path = "/mnt"
    creation_info {
      owner_gid   = 1000
      owner_uid   = 1000
      permissions = "755"
    }
  }

  posix_user {
    gid = 1000
    uid = 1000
  }
}

resource "aws_lambda_event_source_mapping" "async_requests_mapping" {
  event_source_arn = aws_sqs_queue.apicenter_async_request_queue.arn
  enabled          = true
  batch_size       = 1
  function_name    = aws_lambda_function.async_api_worker.arn
}

resource "aws_cloudwatch_log_group" "lambda-async-api-worker-log" {
  name              = "/aws/lambda/apicenter-async-api-worker-${var.environment}"
  retention_in_days = var.log_retention_days

  tags = local.tags
}

resource "aws_cloudwatch_log_subscription_filter" "apicenter-lambda-async-api-worker-log-subscription" {
  name            = "apicenter-lambda-async-api-worker-log-subscription-${var.environment}"
  log_group_name  = aws_cloudwatch_log_group.lambda-async-api-worker-log.name
  filter_pattern  = ""
  destination_arn = data.aws_kinesis_stream.stream.arn
  role_arn        = data.aws_iam_role.log_role.arn
  depends_on = [aws_cloudwatch_log_group.lambda-async-api-worker-log]
}

resource "aws_lambda_function" "apicenter_cache_manager" {
  function_name                  = "apicenter-cache-manager-${var.environment}"
  description                    = "manage apicenter cache"
  role                           = data.aws_iam_role.mart-role.arn
  runtime                        = "java17"
  handler                        = "com.morningstar.cache.function.MessageLambda::handleRequest"
  s3_bucket                      = local.lambda_s3_bucket
  s3_key                         = "apicenter-cache-manager/${var.release_version}/app.jar"
  timeout                        = "600"
  memory_size                    = "1024"
  reserved_concurrent_executions = var.reserved_lambda_concurrent_executions

  vpc_config {
    subnet_ids = [
      module.region_data.private_subnet_1_id[0],
      module.region_data.private_subnet_2_id[0],
      module.region_data.private_subnet_3_id[0]
    ]

    security_group_ids = [
      module.region_data.security_group_private_app[0],
      module.region_data.security_group_private_db[0],
      module.region_data.security_group_private_web[0]
    ]
  }

  tags = local.tags

  environment {
    variables = {
      active_env = var.application_profile
    }
  }
}

resource "aws_cloudwatch_log_group" "apicenter_cache_manager_log" {
  name              = "/aws/lambda/apicenter-cache-manager-${var.environment}"
  retention_in_days = var.log_retention_days

  tags = local.tags
}

resource "aws_cloudwatch_log_subscription_filter" "apicenter-cache-manager-log-subscription" {
  name            = "apicenter-cache-manager-log-subscription-${var.environment}"
  log_group_name  = aws_cloudwatch_log_group.lambda-async-api-worker-log.name
  filter_pattern  = ""
  destination_arn = data.aws_kinesis_stream.stream.arn
  role_arn        = data.aws_iam_role.log_role.arn
  depends_on = [aws_cloudwatch_log_group.lambda-async-api-worker-log]
}

resource "aws_lambda_event_source_mapping" "cache_manager_mapping" {
  event_source_arn = aws_sqs_queue.apicenter_cache_manager_queue.arn
  enabled          = true
  batch_size       = 1
  function_name    = aws_lambda_function.apicenter_cache_manager.arn
}