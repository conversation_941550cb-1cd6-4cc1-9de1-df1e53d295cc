variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

variable "dr_region" {
  default     = "us-west-2"
  description = "The region of the DR resources"
}

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

variable "load_balancers" {
  type = map(object({
    application_name   = string,
    is_internal        = bool,
    idle_timeout       = number
    enable_access_logs = bool,
    extra_security_groups = optional(list(string))
  }))
}

variable "route53_name" {
  type = string
}

variable "bucket_name" {
  type = string
}

variable "data_bucket_name" {
  type = string
}

variable "force_destroy" {
  type        = bool
  description = "Allow to delete non-empty bucket"
}

variable "apicenter_vo_alert_count" {
  type = number
}

variable "vo_endpoint" {
  type = string
  default = ""
}

variable "cache_node_type" {
  type = string
}

variable "cache_num_node_groups" {
  type = number
}

locals {
  is_dr = var.region == var.dr_region
  tags = {
    MANAGED     = "terraform"
    TID         = "DATAAC"
    ENVIRONMENT = var.environmentForTagging
    SERVICEID   = "ts00057"
  }
  env_map = {
    nonprod = "nonp",
    prod    = "prod"
  }
}