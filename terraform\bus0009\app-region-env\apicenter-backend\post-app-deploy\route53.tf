resource "aws_route53_record" "apicenter_route53" {
  #allow_overwrite = true
  zone_id = data.aws_route53_zone.main.zone_id
  name    = "${var.application_name}-${var.environment}.${data.aws_route53_zone.main.name}"
  type    = "CNAME"
  ttl     = "300"

  weighted_routing_policy {
    weight = var.route53_weight
  }
  set_identifier = "${var.environment}${var.is_dr == true ? "-dr" : ""}"
  records = [data.aws_lb.apicenter_lb.dns_name]
}