launchType: FARGATE
serviceName: ph-async-api-service-stg
desiredCount: 1
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0136792b458dbbb96 # private_app
      - sg-0d7a36e9bd3e6b118 # private_web
      - sg-0f160a581db34d981 # private_db
      - sg-0855fcdf523d95fad # private_internal_email_morningstar
      - sg-060c41b97d2313233 # private_mongo
    subnets:
      - subnet-01f195bdee5c8285a
      - subnet-0ebe51e166547c498
      - subnet-0abcce9ab212bec73
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 100
tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION