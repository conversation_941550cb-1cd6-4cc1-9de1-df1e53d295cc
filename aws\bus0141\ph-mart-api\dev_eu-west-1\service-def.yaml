launchType: FARGATE
serviceName: ph-mart-api-fargate-nlb-dev
desiredCount: 1
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0136792b458dbbb96 # private_app
      - sg-0d7a36e9bd3e6b118 # private_web
      - sg-0f160a581db34d981 # private_db
      - sg-0855fcdf523d95fad # private_internal_email_morningstar
    subnets:
      - subnet-01f195bdee5c8285a
      - subnet-0ebe51e166547c498
      - subnet-0abcce9ab212bec73
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent: 200
  minimumHealthyPercent: 0

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
  #- targetGroupArn: <+targetGroupArn> # This expression is used by Harness native B/G deployment step
    containerName: ph-mart-api-fargate-container-dev
    containerPort: 8080

tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION
