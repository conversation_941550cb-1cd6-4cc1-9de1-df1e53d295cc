data "aws_s3_bucket" "s3_bucket" {
  bucket = var.s3_bucket
}

data "aws_iam_role" "iam-role"{
  name = "${var.tsid}-${var.environmentForTagging}-${var.region}-${var.function}-ongoing-dump-role"
}

data "aws_iam_role" "lambda-role"{
  name = "${var.tsid}-lambda-ongoing-s3-trigger-role"
}

module "sec_groups" {
  source           = "./../../../modules/security-groups"
  security_groups  = ["private_app","private_web"]
}

data "template_file" "container_definitions" {
  template = file("./s3-lambda/container_definitions.json")
  vars = {
    image          = "${data.aws_ecr_repository.ecr_repository.repository_url}:${var.release_version}"
    container_name = "${var.tsid}-${var.environmentForTagging}-${var.function}-${var.group}-${var.product}-s3-container"
    region         = var.region
    log_group      = var.log_group
    env            = var.environmentForTagging
    java_opts      = var.java_opts
  }
}

resource "aws_ecs_task_definition" "s3_task_definition" {
  family                    = "${var.tsid}-${var.environmentForTagging}-${var.function}-${var.group}-${var.product}-s3"
  requires_compatibilities  = ["FARGATE"]
  cpu                       = var.task_cpu
  memory                    = var.s3_task_memory
  network_mode             = "awsvpc"
  task_role_arn             = data.aws_iam_role.iam-role.arn
  execution_role_arn        = data.aws_iam_role.iam-role.arn
  container_definitions      = data.template_file.container_definitions.rendered

  tags                      = merge(var.tags)

}

resource "aws_lambda_function" "s3_lambda_function" {
  function_name    = "${var.tsid}-${var.environmentForTagging}-ongoing-s3-trigger"
  #filename         = "./s3-lambda/ongoing_s3_trigger.zip"
  s3_bucket        = var.s3_bucket
  s3_key           = "deployment/lambda/${var.app_env}/ongoing_s3_trigger.zip"
  description      = "Lambda to call s3 ongoing dump when s3 files added"
  role             = data.aws_iam_role.lambda-role.arn
  runtime          = "python3.8"
  handler          = "ongoing_s3_trigger.lambda_handler"
  #source_code_hash = filebase64sha256("./s3-lambda/ongoing_s3_trigger.zip")
  timeout          = 180
  memory_size      = 2048

  tags = var.tags
  environment {
    variables = {
      ENV = var.environmentForTagging
      CONTAINER = data.template_file.container_definitions.vars.container_name
      TASKARN = aws_ecs_task_definition.s3_task_definition.arn
      ACCOUNTID = var.account_id
      REGION = var.region
      NETWORKCONFIG = jsonencode({
        awsvpcConfiguration = {
          subnets = var.subnets_ids,
          securityGroups = module.sec_groups.security_groups_ids
        }
      })
    }
  }
}

resource "aws_lambda_permission" "allow_bucket" {
  statement_id  = "AllowExecutionFromS3Bucket"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.s3_lambda_function.arn
  principal     = "s3.amazonaws.com"
  source_arn    = data.aws_s3_bucket.s3_bucket.arn
}

resource "aws_s3_bucket_notification" "bucket_notification" {
  bucket = data.aws_s3_bucket.s3_bucket.id

  lambda_function {
    lambda_function_arn = aws_lambda_function.s3_lambda_function.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "factset/"
  }

  depends_on = [aws_lambda_permission.allow_bucket]
}