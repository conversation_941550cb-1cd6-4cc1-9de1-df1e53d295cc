name: Copy Artifacts from versioned to qa bucket
items:
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: mart-dps-config/{{sourceVersion}}/dps-config.zip
      expandZip: true
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: mart-data-qa
        key: config/
      # Blue Environment for B/G Deployment
      - accountId: "************"
        region: us-east-1
        bucket: mart-data-qa
        key: blue-env-config/
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-qa
#        key: config/datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/xoi_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-qa
#        key: config/xoi_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/lh_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-qa
#        key: config/lh_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/athena_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-qa
#        key: config/athena_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/ds_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-qa
#        key: config/ds_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/fi_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-qa
#        key: config/fi_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/data-group.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-qa
#        key: config/data-group.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/mapping.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-qa
#        key: config/mapping.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/rdb/{{sourceVersion}}/rdb_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-qa
#        key: config/rdb/rdb_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/rdb/{{sourceVersion}}/rdb_direct.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-qa
#        key: config/rdb/rdb_direct.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/view/{{sourceVersion}}/datapoints_view.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-qa
#        key: config/view/datapoints_view.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/view/{{sourceVersion}}/direct_datapoints_view.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-qa
#        key: config/view/direct_datapoints_view.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/datapoint_whitelist.txt
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-qa
#        key: config/datapoint_whitelist.txt
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/equity_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-qa
#        key: config/equity_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/code_mappings.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-qa
#        key: config/code_mappings.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/calculations.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-qa
#        key: config/calculations.xml
