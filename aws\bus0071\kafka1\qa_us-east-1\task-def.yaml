family: ts00804-qa-app-eqapi-ongoing-dump-kafka1
executionRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/ts00804-qa-us-east-1-app-ongoing-dump-role
taskRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/ts00804-qa-us-east-1-app-ongoing-dump-role
containerDefinitions:
  - name: ts00804-qa-app-eqapi-ongoing-dump-kafka1-container
    image: <+variable.AWS_NONPROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/eqapi/api-v2:<+serviceVariables.ImageTag>
    cpu: 2048
    essential: true
    command: 
      - "--spring.profiles.active=qa"
      - "--resumeTopic=default"
      - "--workflows=ged_equitydata_market_capital,ged_equitydata_ecs_component_data,ged_equitydata_ecs_outstanding_data,ged_equitydata_capital_calculations_equity_component_level,ged_equitydata_stock_options_restricted_stock_units_and_warrants_company_level,ged_equitydata_stock_options_restricted_stock_units_and_warrants_plan_level,ged_equitydata_stock_options_restricted_stock_units_and_warrants_tranche_level,ged_equitydata_diluted_market_capital,ged_equitydata_diluted_enterprise_value_calculations,ged_equitydata_trailing_return,ged_equitydata_market_return,ged_equitydata_spin_values,ged_equitydata_daily_standard_deviation,ged_equitydata_total_shares_outstanding,ged_equitydata_price_multiples,ged_equitydata_calculation_fiscal_period_dividend_metrics,ged_equitydata_margins,ged_equitydata_calculation_fiscal_period_financial_health_ratios"
           
    environment:
      - name: JAVA_OPTS
        value: -XX:MaxRAMPercentage=80.0 -XX:MinRAMPercentage=40.0 -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC
      - name: APP_NAME
        value: ongoing
      - name: ENV
        value: staging
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /aws/ecs/qa-eqapi-ongoing-dump
        awslogs-region: <+infra.region>
        awslogs-stream-prefix: ecs
cpu: '2048'
memory: '948'
requiresCompatibilities:
  - EC2
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>