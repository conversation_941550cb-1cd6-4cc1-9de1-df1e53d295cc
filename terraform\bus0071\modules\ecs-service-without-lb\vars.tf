variable "vpc_tag" {}
variable "tags" {}
variable "environment" {}
variable "region" {}
variable "group" {}
variable "product" {}
variable "log_group" {}

variable "task_cpu" {}
variable "task_mem" {}
variable "desired_count" {}

variable "image_tag" {}
variable "create_resource" {}
variable "ecs_cluster_name" {}
variable "create_ecs_cluster" {}
variable "account_id" {}

variable "ecs_role_arn" {}
variable "create_repository_resource" {}
variable "security_groups" {
  default = ["private_app","private_web"]
}
variable "subnets_ids" {}

variable "tsid" {}
variable "function" {}