launchType: FARGATE
serviceName: velo-admin-service-prod
desiredCount: 1
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0e7ef1010d6acddf8 # private_web
      - sg-0d788a43acb654693 # private_app
      - sg-078ee4651c9f093eb # private_db
    subnets:
      - subnet-007aa27f3f50932a2 # us-east-1c-private
      - subnet-08f032a54afd01bcc # us-east-1a-private
      - subnet-0adafb2337a7adb7a # us-east-1b-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent: 200
  minimumHealthyPercent: 50

healthCheckGracePeriodSeconds: 300

loadBalancers:
  - targetGroupArn: arn:aws:elasticloadbalancing:us-east-1:921072466220:targetgroup/velo-admin-target-group-prod/49ed68a3fb6022e9
    containerName: velo-admin-container-prod
    containerPort: 8080

tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>

propagateTags: TASK_DEFINITION