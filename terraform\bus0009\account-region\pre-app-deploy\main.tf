provider "aws" {
  region = var.region
  assume_role {
    role_arn     = var.use_role_to_assume ? var.role_to_assume : null
    session_name = var.session_name
  }
}

provider "aws" {
  region = var.dr_region
  alias = "dr"

  assume_role {
    role_arn      = var.use_role_to_assume ? var.role_to_assume : null
    session_name  = "${var.session_name}_DR"
  }
}

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.58"
    }
  }
  backend "s3" {}
}