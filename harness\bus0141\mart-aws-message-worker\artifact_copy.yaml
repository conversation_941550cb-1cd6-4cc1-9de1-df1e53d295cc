name: Copy Artifacts to S3
items:
  - sourceFile: release/message_worker.zip
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: mart-aws-message-worker/{{version}}/delta-msg-worker-function.zip
  - sourceFile: release/upstream-messager.zip
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: mart-aws-message-worker/{{version}}/delta-msg-relay-function.zip
  - sourceFile: release/lake_messager.zip
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: mart-aws-message-worker/{{version}}/delta-lake-relay-function.zip