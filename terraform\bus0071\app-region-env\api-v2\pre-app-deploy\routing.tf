module "r53" {
  source      = "./../../../modules/route53-zone"
  environment = "${var.environmentForTagging}"
}

resource "aws_route53_record" "r53_record_live" {
  zone_id = module.r53.r53_team_zone_id
  name    = "${var.environmentForTagging}-${var.group}-restful-api"
  type    = "CNAME"
  ttl     = "300"
  records = [module.eqapi-restful-api-ecs.alb_dns_name]

  weighted_routing_policy {
    weight = var.r53_routing_live_weightage
  }

  set_identifier = var.app_env
}

output "r53_url" {
  value = "${var.environmentForTagging}-${var.group}-restful-api.${module.r53.r53_team_zone_name}"
}

output "alb_dns_name" {
  value = module.eqapi-restful-api-ecs.alb_dns_name
}