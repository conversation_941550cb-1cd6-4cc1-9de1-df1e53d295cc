name: Copy Artifacts from non-prod to prod account
items:
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: mart-aws-loader-trigger/{{sourceVersion}}/function.zip
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: mart-aws-loader-trigger/{{targetVersion}}/function.zip
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: riskmodel-athena-mapping-trigger/{{sourceVersion}}/riskModelAthenaMappingTriggerFunction.zip
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: riskmodel-athena-mapping-trigger/{{targetVersion}}/riskModelAthenaMappingTriggerFunction.zip
