launchType: FARGATE
serviceName: ph-async-api-service-prod
desiredCount: 1
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0a570b98b80bc8079  #private_web
      - sg-012adae20d841f7a2  #private_app
      - sg-055d33ddd7b542fdd  #private_db
      - sg-04d3ab2be2c0244e6  #private_mongo
    subnets:
      - subnet-0415e48b172d767a8
      - subnet-0bd1845ab02324d6b
      - subnet-02b2ccf2c0a676f9d
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 100
tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION