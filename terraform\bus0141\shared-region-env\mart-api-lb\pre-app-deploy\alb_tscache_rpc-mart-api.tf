//gRPC target groups for tscache-mart-rpc service
//listener 443 with https protocol for gRPC traffic
resource "aws_lb_target_group" "tscache_rpc_mart_api_nlb_fargate_target_group_1" {
  name        = "tscache-rpc-mart-api-tg-nlb-${var.environment}-1"
  target_type = "ip"
  port        = 9090
  protocol    = "HTTP"
  protocol_version = "GRPC"
  vpc_id      = var.vpc
  load_balancing_algorithm_type = "least_outstanding_requests"

  health_check {
    enabled             = true
    healthy_threshold   = "3"
    interval            = "30"
    protocol            = "HTTP"
    matcher             = "0"  // gRPC uses status code 0 for OK
    timeout             = "3"
    port                = "9090"
    unhealthy_threshold = "3"
  }
  tags = local.tags
}

resource "aws_lb_target_group" "tscache_rpc_mart_api_nlb_fargate_target_group_2" {
  name        = "tscache-rpc-mart-api-tg-nlb-${var.environment}-2"
  target_type = "ip"
  port        = 9090
  protocol    = "HTTP"
  protocol_version = "GRPC"
  vpc_id      = var.vpc
  load_balancing_algorithm_type = "least_outstanding_requests"

  health_check {
    enabled             = true
    healthy_threshold   = "3"
    interval            = "30"
    protocol            = "HTTP"
    matcher             = "0"  // gRPC uses status code 0 for OK
    timeout             = "3"
    port                = "9090"
    unhealthy_threshold = "3"
  }
  tags = local.tags
}

// gRPC routing rule for HTTPS listener on port 443
resource "aws_lb_listener_rule" "tscache_rpc_api_routing_blue" {
  count        = var.use_tscache_rpc_listener_rule ? 1 : 0
  listener_arn = aws_lb_listener.https443.arn
  priority     = 75  // Setting priority between existing rules
  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.tscache_rpc_mart_api_nlb_fargate_target_group_1.arn
  }
  git {
    http_header {
      http_header_name = "content-type"
      values = [
        "application/grpc*"
      ]
    }
  }
  condition {
    path_pattern {
      values = [
        "/rpc/*"
      ]
    }
  }

  lifecycle {
    #noinspection HILUnresolvedReference
    ignore_changes = [
      # Ignoring because harness will change the value during a B/G swap
      action[0].target_group_arn
    ]
  }

  tags = merge(local.tags, {
    NAME = "TS RPC Blue"
  })
}

// gRPC routing rule for HTTPS listener on port 8443 (for Blue/Green deployment)
resource "aws_lb_listener_rule" "tscache_rpc_api_routing_green" {
  count        = var.use_bg_deployment && var.use_tscache_rpc_listener_rule ? 1 : 0
  listener_arn = aws_lb_listener.https8443[0].arn
  priority     = 75  // Setting priority between existing rules
  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.tscache_rpc_mart_api_nlb_fargate_target_group_2.arn
  }
  condition {
    http_header {
      http_header_name = "content-type"
      values = [
        "application/grpc*"
      ]
    }
  }

  lifecycle {
    #noinspection HILUnresolvedReference
    ignore_changes = [
      # Ignoring because harness will change the value during a B/G swap
      action[0].target_group_arn
    ]
  }

  tags = merge(local.tags, {
    NAME = "TS RPC Green"
  })
}
