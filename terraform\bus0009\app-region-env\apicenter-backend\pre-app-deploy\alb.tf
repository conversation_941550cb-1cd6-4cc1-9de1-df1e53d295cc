resource "aws_lb_target_group" "apicenter_lb_target_group" {
  name                 = "${var.application_name}-tg-${var.environment}"
  port                 = 80
  protocol             = "HTTP"
  target_type          = "ip"
  vpc_id               = var.vpc_id
  deregistration_delay = 20
  tags                 = local.tags

  health_check {
    path              = "/support/login"
    port              = var.host_port
    healthy_threshold = 3
    interval          = 30
    timeout           = 10
  }

  lifecycle {
    create_before_destroy = true
    ignore_changes        = [name]
  }

  depends_on = [data.aws_lb.apicenter_lb]
}

resource "aws_lb_listener_rule" "apicenter_listener_rule" {
  listener_arn = data.aws_lb_listener.apicenter_lb_listener.arn
  priority     = var.listener_priority

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.apicenter_lb_target_group.id
  }

  condition {
    path_pattern {
      values = ["/support*"]
    }
  }
  condition {
    host_header {
      values = concat(var.host_header, [data.aws_lb.apicenter_lb.dns_name])
    }
  }
  depends_on = [aws_lb_target_group.apicenter_lb_target_group]
}

resource "aws_lb_listener_certificate" "fund_api_listener_certificate" {
  count           = var.environment == "prod" ? 1 : 0
  listener_arn    = data.aws_lb_listener.apicenter_lb_listener.arn
  certificate_arn = data.aws_acm_certificate.ms_certificate[count.index].arn
}


