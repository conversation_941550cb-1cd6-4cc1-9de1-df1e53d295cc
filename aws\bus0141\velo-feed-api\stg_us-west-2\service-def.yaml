launchType: FARGATE
serviceName: velo-feed-api-service-stg
desiredCount: 0
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-03aae3fda738ccd3f # private_web
      - sg-0cb0b481497065818 # private_app
      - sg-0a719818294fd3f0b # private_db
    subnets:
      - subnet-03d560dd0e5253099 # us-west-2c-private
      - subnet-0cfbd3bd59d7c2cad # us-west-2b-private
      - subnet-084b8c666e71a339f # us-west-2a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 50

healthCheckGracePeriodSeconds: 10

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: velo-feed-api-container-stg
    containerPort: 8080

enableExecuteCommand: true
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION