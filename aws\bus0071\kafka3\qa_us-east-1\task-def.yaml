family: ts00804-qa-app-eqapi-ongoing-dump-kafka3
executionRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/ts00804-qa-us-east-1-app-ongoing-dump-role
taskRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/ts00804-qa-us-east-1-app-ongoing-dump-role
containerDefinitions:
  - name: ts00804-stg-app-eqapi-ongoing-dump-kafka3-container
    image: <+variable.AWS_NONPROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/eqapi/api-v2:<+serviceVariables.ImageTag>
    cpu: 2048
    essential: true
    command: 
      - "--spring.profiles.active=qa"
      - "--resumeTopic=default"
      - "--workflows=ged_equitydata_daily_dividend_residual_risk_and_return_sensitivity,ged_equitydata_daily_non_dividend_residual_risk_and_return_sensitivity,ged_equitydata_balance_sheet,ged_equitydata_monthly_standard_deviation,ged_equitydata_dividend_residual_risk_and_return_sensitivity,ged_equitydata_non_dividend_residual_risk_and_return_sensitivity,ged_equitydata_company_information,ged_equitydata_ecs_event_data,ged_equitydata_ecs_profiling_data,ged_equitydata_ecs_underlying_component_data,ged_equitydata_regulatory_data_for_us_bank_holding_companies,ged_equitydata_net_asset_value,ged_equitydata_net_asset_value_per_share,ged_equitydata_bank_reserve_ratio_duration,ged_equitydata_bank_reserve_ratio_spot,ged_equitydata_fund_from_operations,ged_equitydata_fund_from_operations_per_share,ged_equitydata_segments_enhanced,ged_equitydata_balance_sheet_calculations,ged_equitydata_profitability,ged_equitydata_balance_sheet_calculations_per_share,ged_equitydata_income_statement_calculations_per_share,ged_equitydata_profitability_per_share,ged_equitydata_balance_sheet_reported_averages,ged_equitydata_price_yields,ged_equitydata_dividend_metrics"
    environment:
      - name: JAVA_OPTS
        value: -XX:MaxRAMPercentage=80.0 -XX:MinRAMPercentage=40.0 -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC
      - name: APP_NAME
        value: ongoing
      - name: ENV
        value: staging
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /aws/ecs/qa-eqapi-ongoing-dump
        awslogs-region: <+infra.region>
        awslogs-stream-prefix: ecs
cpu: '2048'
memory: '948'
requiresCompatibilities:
  - EC2
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>