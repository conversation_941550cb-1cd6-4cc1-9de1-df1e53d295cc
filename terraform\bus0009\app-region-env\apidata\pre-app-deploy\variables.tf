variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

variable "application_name" {
  type = string
}

variable "vpc_id" {
  type = string
}

variable "host_port" {
  type    = number
  default = 8080
}

variable "deregistration_delay" {
  type    = number
}

variable "listener_certificate_arn" {
  type = string
}

variable "listener_priority" {
  type = number
}

variable "host_header" {
  type = list(string)
}

variable "kinesis_stream_count" {
  type    = number
  default = 0
}

variable "kinesis_stream_name" {
  type = string
}

variable "log_retention_days" {
  type = number
}

locals {
  alb_map = {
    dev = "fundapi-alb-nonp",
    uat = "fundapi-alb-prod",
    prod = "fundapi-alb-prod"
  }

  tags = {
      SERVICEID   = "ts00057",
      ENVIRONMENT = var.environmentForTagging,
      PID         = "PID0029",
      TID         = "DATAAC",
      MANAGED     = "terraform"
    }
}
