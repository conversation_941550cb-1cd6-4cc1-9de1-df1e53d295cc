data "aws_iam_role" "iam-role"{
  name = "${var.tsid}-${var.environment}-${var.region}-${var.function}-${var.product}-role"
}

resource "aws_ecs_service" "ecs_service" {
  count                               = var.create_resource
  name                                = var.ecs_service_name
  cluster                             = var.ecs_cluster_name
  task_definition                     = aws_ecs_task_definition.task_ec2.0.arn
  deployment_minimum_healthy_percent  = 0
  force_new_deployment                = true
  desired_count                       = var.create_resource

  deployment_controller {
    type = "ECS"
  }

  placement_constraints {
    type = "memberOf"
    expression = "attribute:service==${var.ecs_cluster_name}"
  }
  propagate_tags                        = "TASK_DEFINITION"
  tags                                  = merge(var.tags)
}

resource "aws_ecs_task_definition" "task_ec2" {
  requires_compatibilities = ["EC2"]
  container_definitions    = data.template_file.container_definitions.0.rendered
  family                   = var.ecs_service_name
  task_role_arn            = data.aws_iam_role.iam-role.arn
  execution_role_arn       = data.aws_iam_role.iam-role.arn

  tags                     = merge(var.tags)
  count                    = var.create_resource
}

data "template_file" "container_definitions" {
  count    = var.create_resource
  template = file("${path.module}/container_definitions.json")
  vars     = {
    image             = var.image
    #app_port         = var.app_port
    container_name    = "${var.ecs_service_name}-container"
    region            = var.region
    log_group         = var.log_group_name
    env               = var.environment
    command           = jsonencode(var.command)
    java_opts         = var.java_opts
    app_name          = var.app_name
    cpu               = var.cpu
    memoryReservation = var.memory
  }
}
