variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

locals {
  service_name = "${var.tsid}-${var.environmentForTagging}-${var.function}-${var.group}-ongoing-dump"
  image_name   = "${data.aws_ecr_repository.ecr_repository.repository_url}:${var.release_version}"
  workflows1   = join(",", var.group1_workflows)
  workflows2   = join(",", var.group2_workflows)
  workflows3   = join(",", var.group3_workflows)
}

locals {
  prod_kaffe_url             = "http://aws-kafka-monitor:8082/consumergroup/"
  env_suffix                 = "-${var.environmentForTagging}"
  message = <<RUNBOOK
  Problem detected with the topic, refer the runbook https://mswiki.morningstar.com/display/SWDO/Direct+SRE+Runbook+-+Equity+Data+Product+-+Equity+API
  to fix the issue. 
  RUNBOOK
}

variable "release_version" {}
variable "tsid" {}
variable "app_env" {}

variable "function" {}
variable "product" {}
variable "group" {}
variable "s3_bucket" {}
variable "subnets_ids" {}
variable "account_id" {}
variable "instance_type" {}
variable "instance_size" {}
variable "ec2_keypair" {}
variable "ami_id" {}
variable "task_cpu" {}
variable "task_memory" {}
variable "s3_task_memory" {}
variable "log_group" {}
variable "java_opts" {}
variable "java_opts_batch" {}
variable "web_java_opts" {}
variable "web_task_mem" {}
variable "web_task_cpu" {}

variable "vpc_tag" {}
variable "tags" {}
variable "web_tags" {}
variable "esg_tags" {}
variable "batch_tags" {}
variable "create_resource" {}
variable "min_capacity" {}
variable "max_capacity" {}
variable "access_log_bucket" {}
variable "certificate_domain" {}

variable "batch_instance_type" {}
variable "batch_instance_volume_type" {}
variable "batch_instance_volume_size" {}
variable "batch_instance_volume_iops" {}

variable "redis_node_type" {}
variable "redis_node_num" {}
variable "redis_shard_num" {}
variable "redis_replicas_per_shard" {}
variable "redis_engine_version" {}

variable "es_instance_type" {}
variable "es_instance_size" {}
variable "es_volume_size" {}

variable "desired_count" {}
variable "r53_routing_live_weightage" {}
variable "alb_healthy_threshold" {}
variable "alb_unhealthy_threshold" {}
variable "alb_health_check_timeout" {}
variable "alb_health_check_path" {}
variable "alb_health_check_interval" {}

variable "resume_offset" {}
variable "minDlLoadDates" {}
variable "group1_workflows" {}
variable "group2_workflows" {}
variable "group3_workflows" {}

variable "create_resource_kafka1" {}
variable "create_resource_kafka2" {}
variable "create_resource_kafka3" {}
variable "create_resource_datalake1" {}

#sns
variable "sns_topic_name" {}
variable "sns_topic_recepient" {}