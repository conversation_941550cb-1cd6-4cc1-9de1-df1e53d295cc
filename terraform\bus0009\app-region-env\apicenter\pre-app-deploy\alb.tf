resource "aws_alb_target_group" "main" {
  name                 = "${var.name}-tg-${var.environment}"
  port                 = 80
  protocol             = "HTTP"
  vpc_id               = data.aws_vpc.main.id
  target_type          = "ip"
  deregistration_delay = 20
  tags                 = local.tags
  health_check {
    healthy_threshold   = "3"
    interval            = "30"
    protocol            = "HTTP"
    matcher             = "200"
    timeout             = "3"
    path                = var.health_check_path
    unhealthy_threshold = "2"
  }
}

resource "aws_lb_listener_certificate" "apicenter_listener_certificate" {
  listener_arn    = data.aws_lb_listener.apicenter_lb_listener.arn
  certificate_arn = data.aws_acm_certificate.ms_certificate.arn
}

resource "aws_alb_listener_rule" "host_based_routing" {
  listener_arn = data.aws_lb_listener.apicenter_lb_listener.arn
  priority     = var.listener_priority

  action {
    type             = "forward"
    target_group_arn = aws_alb_target_group.main.arn
  }

  condition {
    path_pattern {
      values = ["/*"]
    }
  }
  condition {
    host_header {
      values = concat(var.host_header, [data.aws_lb.apicenter_lb.dns_name])
    }
  }

  depends_on = [aws_alb_target_group.main]
}
