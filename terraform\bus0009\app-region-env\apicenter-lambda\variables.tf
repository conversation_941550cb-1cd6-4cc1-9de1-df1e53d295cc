variable "region" {
  description = "Region"
  default     = "us-east-1"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

variable "release_version" {
  type = string
}

variable "dr_region" {
  default     = "us-west-2"
  description = "The region of the DR resources"
}

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################
variable "account_env" {
}

variable "task_cpu" {
  description = "cpu for mart api instance"
}

variable "task_mem" {
}

variable "deploy_max" {
  description = "maximum instance deploy number"
}

variable "deploy_min" {
  description = "minimum instance deploy number"
}

variable "reserved_lambda_concurrent_executions" {
  default     = "50"
  description = "number of reserved lambda concurrent executions"
}

variable "application_profile" {
  type = string
}

variable "dfd_lambda_access_group"{
  description = "number of reserved lambda concurrent executions"
}

variable "log_retention_days" {
  type = number
}

locals {
  tags = {
    MANAGED     = "terraform"
    ENVIRONMENT = var.environmentForTagging,
    PID         = "PID0029"
    SERVICEID   = "ts00057"
    TID         = "DATAAC"
    FUNCTION    = "app"
  }
  lambda_s3_bucket = "velo-deploy-${var.account_env}-${var.region}"
  velo_role_env_map = {
    dev  = "stg",
    uat  = "prod",
    prod = "prod"
  }
  mart_role_env_map = {
    dev  = "stg",
    uat  = "uat",
    prod = "prod"
  }
}