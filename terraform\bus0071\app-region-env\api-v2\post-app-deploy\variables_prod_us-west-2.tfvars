region="us-west-2"
role_to_assume="arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment="prod"
environmentForTagging="prd"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

app_env         = "prddr"

min_capacity    = 0
max_capacity    = 0
create_resource = 1

tags = {
  ENVIRONMENT    = "prd"
  ORG            = "dataservice"
  MANAGED        = "terraform"
  PID            = "PID0497"
  SERVICE        = "Equity API V2"
  TID            = "EQAPI"
  SERVICEID      = "ts00804"
  BUSID          = "bus0071"
  FUNCTION       = "app"
  Name           = "ts00804-prd-eqapi-restful-api"
}