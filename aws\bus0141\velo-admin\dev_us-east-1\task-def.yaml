family: velo-admin-definition-dev
executionRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/velo-stand-alone-dev
taskRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/velo-stand-alone-dev
containerDefinitions:
  - name: velo-admin-container-dev
    image: <+variable.AWS_NONPROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/velo-admin-ecr:<+serviceVariables.ImageTag>
    cpu: 512
    memory: 1024
    portMappings:
      - hostPort: 8080
        protocol: tcp
        containerPort: 8080
    essential: true
    environment:
      - name: ACTIVE_ENV
        value: dev
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /ecs/velo-admin-dev
        awslogs-region: us-east-1
        awslogs-stream-prefix: ecs

cpu: '512'
memory: '1024'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>