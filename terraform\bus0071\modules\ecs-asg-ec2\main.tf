//data "aws_ami" "linux_ami" {
//  most_recent = true
//  filter {
//    name = "name"
//    values = [
//      "Morningstar Amazon Linux 2 ECS Optimized *"]
//  }
//  owners = [
//    "487162776507"]
//}

locals {
  instance_attributes = {
    service = var.ecs_cluster_name
  }
}

data "aws_iam_instance_profile" "iam-instance-profile"{
  name = "${var.tsid}-${var.environment}-${var.region}-${var.function}-${var.product}-instance-profile"
}

module "vpc" {
  source = "../../modules/vpc"
  vpc_tag = var.vpc_tag
}

module "private_subnets" {
  source = "../../modules/subnets"
  vpc_id = module.vpc.vpc_id
  filter_tag_function = "PRIVATE"
}

module "sec_groups" {
  source = "../../modules/security-groups"
  security_groups = var.security_groups
}

resource "aws_autoscaling_group" "autoscaling_group" {
  name_prefix         = "${var.ecs_cluster_name}-asg"
  min_size            = 0
  max_size            = var.instance_size
  desired_capacity    = var.instance_size
  force_delete        = true
  vpc_zone_identifier = module.private_subnets.subnets_ids

  launch_template {
    id      = aws_launch_template.launch_template.0.id
    version = aws_launch_template.launch_template.0.latest_version
  }
  tags                = var.esg_tags
  count               = var.create_resource
}

resource "aws_launch_template" "launch_template" {
  name_prefix            = "${var.ecs_cluster_name}-lt"
  //image_id               = data.aws_ami.linux_ami.id
  image_id               = "resolve:ssm:/aws/service/ecs/optimized-ami/amazon-linux-2023/recommended/image_id"
  instance_type          = var.instance_type
  vpc_security_group_ids = module.sec_groups.security_groups_ids
  iam_instance_profile {
    name                 = data.aws_iam_instance_profile.iam-instance-profile.name
  }

  key_name               = var.ec2_keypair
  ebs_optimized          = true
  user_data              = base64encode(data.template_file.user_data_template.rendered)

  //https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ebs-volume-types.html
  //  block_device_mappings {
  //    device_name              = "/dev/xvda"
  //    ebs {
  //      volume_size            = var.instance_volume_size
  //      volume_type            = var.instance_volume_type
  //      //      snapshot_id            = "snap-0328a56679e9aa5d4"
  //      delete_on_termination  = true
  //      encrypted              = false
  //      iops                   = var.instance_volume_iops
  //      //      tags                   = var.tags
  //    }
  //  }
  lifecycle {
    create_before_destroy = true
  }
  monitoring {
    enabled               = true
  }
  tag_specifications {
    resource_type         = "volume"
    tags                  = var.tags
  }
  tag_specifications {
    resource_type         = "instance"
    tags                  = var.tags
  }
  tags                  = var.tags
  count                 = var.create_resource

}

data "template_file" "user_data_template"{
  template           = file("${path.module}/user_data.tpl")
  vars = {
    ecs_cluster_name = var.ecs_cluster_name
    ecs_instance_attributes = jsonencode(local.instance_attributes)
  }
}