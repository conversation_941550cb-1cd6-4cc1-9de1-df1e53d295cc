variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

variable "name" {}

variable "is_dr" {
  default = false
}

variable "vpc" {}

variable "health_check_path" {
  default = "/udm.html"
}

variable "host_header" {
  type = list(string)
}

variable "listener_priority" {
  type = number
}

variable "log_retention_days" {
  type = number
}

locals {
  task_family_name = "${var.name}-${var.environment}"
  env_map = {
    dev  = "qa",
    uat  = "",
    prod = ""
  }
  alb_map = {
    dev  = "apicenter-alb-nonp",
    uat  = "apicenter-alb-prod",
    prod = "apicenter-alb-prod"
  }
  kinesis_map = {
    dev  = "non-prod",
    uat  = "prod",
    prod = "prod"
  }
  tags = {
    Name        = "apicenter"
    ENVIRONMENT = var.environmentForTagging,
    PID         = "PID0029",
    SERVICEID   = "ts00057",
    TID         = "DATAAC",
    FUNCTION    = "app",
    MANAGED     = "terraform"
  }
}




