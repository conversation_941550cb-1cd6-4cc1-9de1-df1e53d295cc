module "region_data" {
  source = "git::ssh://msstash.morningstar.com/cloudsvc/tf-module-infrastructure-data.git?ref=feature/terraform1.3"
}

data "aws_region" "current" {}

data "aws_security_groups" "apicenter_extra_security_groups" {
  for_each = {
    for k, v in var.load_balancers : k => v
    if v.extra_security_groups != null
  }
  filter {
    name   = "vpc-id"
    values = [element(module.region_data.vpc_id, 0)]
  }
  filter {
    name   = "group-name"
    values = each.value["extra_security_groups"]
  }
}

data "aws_acm_certificate" "route53_certificate" {
  domain   = "*.${var.route53_name}"
  statuses = ["ISSUED"]
}

data "aws_elb_service_account" "main" {}

data "aws_iam_policy_document" "apicenter_bucket_policy" {
  statement {
    principals {
      type        = "AWS"
      identifiers = [data.aws_elb_service_account.main.arn]
    }

    actions = ["s3:PutObject"]

    resources = [
      "${aws_s3_bucket.apicenter_bucket.arn}/*"
    ]
  }
}