deploy_role_name: mstar-engr-cross-account-deploy

# ECS Services
ecs_services:
  - service_name: velo-standalone-service-dev  # Website
    cluster_name: velo-stand-alone-cluster-dev
    autoscaling: true
    min_capacity: 1
  - service_name: velo-schedule-service-dev  # Scheduler
    cluster_name: velo-stand-alone-cluster-dev
    autoscaling: true
    min_capacity: 1
  - service_name: velo-feed-api-service-dev  # External API
    cluster_name: velo-stand-alone-cluster-dev
    autoscaling: true
    min_capacity: 1
  - service_name: mart-monitor-service-dev  # Mart Monitor
    cluster_name: velo-stand-alone-cluster-dev
    autoscaling: false
    min_capacity: 1

# SSM Parameter Store
ssm:
  source_region: us-east-1
  sync_tag_key: SyncAcrossRegions

scheduler:
  - template:
      location: 'templates/sqs_schedule_switch.json.j2'
      variables:
        message_target_group: velo
        url: https://sqs.us-east-1.amazonaws.com/************/velo-scheduler-sqs-dev.fifo
  - template:
      location: 'templates/sqs_schedule_switch.json.j2'
      variables:
        message_target_group: fdo
        url: https://sqs.us-east-1.amazonaws.com/************/velo-scheduler-sqs-dev.fifo

# Elasticsearch Configuration
es:
  - domain_name: dev-change-notification
    template:
      location: 'templates/es-change-notification.json.j2'
      variables:
        domain_name: dev-change-notification
        access_policy: '{\"Version\": \"2012-10-17\",\"Statement\": [{\"Effect\": \"Allow\",\"Principal\": {\"AWS\": \"*\"},\"Action\": \"es:*\",\"Resource\": \"arn:aws:es:us-west-2:************:domain/dev-change-notification/*\"}]}'
        subnets:
          - subnet-03d560dd0e5253099
          - subnet-0cfbd3bd59d7c2cad
          - subnet-084b8c666e71a339f
        security_group_ids:
          - sg-03aae3fda738ccd3f
        tags:
          TID: DATAAC
          PID: PID0574
          SERVICEID: ts01004
    route53:
      template:
        location: 'templates/route53-change-notification.json.j2'
        variables:
          zone_id: Z1763QDC3XWVS7
          domain_name: datasvc-change-notification-stg-dr.date7ebe.easn.morningstar.com
    snapshot:
      lambda: backup-snapshot-dev
      restore:
        payload:
          task: changeNotificationES
          action: restore_change_notification
      create:
        payload:
          task: changeNotificationES

# Route53 Configuration
route53:
  zone_id: Z1763QDC3XWVS7
  records:
    - name: Website
      lb_name: velo-standalone-lb-stg
      template:
        location: 'templates/route53/website.json.j2'
        variables:
          identifier: stg
          domain_name: velo-standalone.date7ebe.easn.morningstar.com
    - name: External
      lb_name: velo-feed-api-lb-stg
      template:
        location: 'templates/route53/external.json.j2'
        variables:
          domain_name: velo-feed-api-dev.date7ebe.easn.morningstar.com