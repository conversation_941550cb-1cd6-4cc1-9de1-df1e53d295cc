launchType: FARGATE
serviceName: essentials-image-loader-service-dev
desiredCount: 1
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-03afef83e7d6959c8 # private_app
      - sg-08a83e28c2216dc3c # private_web
      - sg-0fd2a98817ac752f7 # private_db
      - sg-094caeaa431cb2437 # private_postfeed
    subnets:
      - subnet-08f0ece89d42f7d73
      - subnet-0a1c0ff43ccc5652c
      - subnet-00fa9e860f858a4a2
    assignPublicIp: DISABLED
deploymentConfiguration:
  maximumPercent: 200
  minimumHealthyPercent: 50
healthCheckGracePeriodSeconds : 300
schedulingStrategy : REPLICA
loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: essentials-image-loader-container-dev
    containerPort: 8080
tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION
