data "aws_ecr_repository" "ecr_repository" {
  name  = "${var.group}/${var.product}"
}
data "aws_iam_role" "eqapi-onetime-dump-role"{
  name = "${var.tsid}-${var.environmentForTagging}-us-east-1-${var.function}-onetime-dump-role"
}

data "aws_iam_role" "eqapi-restful-api-role"{
  name = "${var.tsid}-${var.environmentForTagging}-us-east-1-${var.function}-restful-api-role"
}

data "aws_iam_instance_profile" "eqapi-onetime-dump-instance-profile"{
  name = "${var.tsid}-${var.environmentForTagging}-${var.region}-${var.function}-onetime-dump-instance-profile"
}
