family: stg-entitlement-api
executionRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/velo-stand-alone-stg
taskRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/velo-stand-alone-stg
containerDefinitions:
  - name: stg-entitlement-api
    image: <+variable.AWS_NONPROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/velo-entitlement-api-ecr:<+serviceVariables.ImageTag>
    cpu: 1024
    memory: 3072
    portMappings:
      - hostPort: 8443
        protocol: tcp
        containerPort: 8443
    essential: true
    environment:
      - name: aws_region
        value: us-east-1
      - name: ACTIVE_ENV
        value: stg-us-east-1
      - name: datasource_session_variable
        value: <+serviceVariables.DATASOURCE_SESSION_VARIABLE>
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /ecs/entitlement-api-stg
        awslogs-region: us-east-1
        awslogs-stream-prefix: ecs

cpu: '1024'
memory: '3072'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: ORG
    value: <+serviceVariables.ORG>
  - key: TID
    value: <+variable.TID>
  - key: PID
    value: <+serviceVariables.PID>
  - key: MANAGED
    value: <+serviceVariables.MANAGED>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>