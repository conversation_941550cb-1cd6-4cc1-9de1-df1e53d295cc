resource "aws_sqs_queue" "apicenter_dfd_generator_queue" {
  name                        = "apicenter-dfd-generator-${var.environment}"
  content_based_deduplication = false
  visibility_timeout_seconds  = 600
  delay_seconds               = 0
  max_message_size            = 262144
  message_retention_seconds   = 345600
  receive_wait_time_seconds   = 20

  tags = local.tags
}

resource "aws_sqs_queue_policy" "access_control_dfd" {
  queue_url = aws_sqs_queue.apicenter_dfd_generator_queue.id

  policy = <<POLICY
  {
  "Version": "2012-10-17",
  "Id": "${aws_sqs_queue.apicenter_dfd_generator_queue.arn}/SQSDefaultPolicy",
  "Statement": [
    {
      "Sid": "Sid1537946410847",
      "Effect": "Allow",
      "Principal": ${format("{ \"AWS\": %s }", jsonencode(var.dfd_lambda_access_group))},
      "Action": "SQS:*",
      "Resource": "${aws_sqs_queue.apicenter_dfd_generator_queue.arn}"
    }
  ]
}
POLICY
}

resource "aws_sqs_queue" "apicenter_async_request_queue" {
  name                        = "apicenter-async-requests-${var.environment}"
  visibility_timeout_seconds  = 600
  delay_seconds               = 0
  max_message_size            = 262144
  message_retention_seconds   = 345600
  receive_wait_time_seconds   = 20

  tags = local.tags
}

resource "aws_sqs_queue_policy" "access_control_async" {
  queue_url = aws_sqs_queue.apicenter_async_request_queue.id

  policy = <<POLICY
  {
  "Version": "2012-10-17",
  "Id": "${aws_sqs_queue.apicenter_async_request_queue.arn}/SQSDefaultPolicy",
  "Statement": [
    {
      "Sid": "Sid1537946410847",
      "Effect": "Allow",
      "Principal": {
        "AWS": [
          "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/mart-role-${var.environment}"
        ]
      },
      "Action": "SQS:*",
      "Resource": "${aws_sqs_queue.apicenter_async_request_queue.arn}"
    }
  ]
}
POLICY
}

resource "aws_sqs_queue" "apicenter_cache_manager_queue" {
  name                        = "apicenter-cache-manager-${var.environment}"
  visibility_timeout_seconds  = 600
  delay_seconds               = 0
  max_message_size            = 262144
  message_retention_seconds   = 345600
  receive_wait_time_seconds   = 20

  tags = local.tags
}

resource "aws_sqs_queue_policy" "access_control_cache" {
  queue_url = aws_sqs_queue.apicenter_cache_manager_queue.id

  policy = <<POLICY
  {
  "Version": "2012-10-17",
  "Id": "${aws_sqs_queue.apicenter_cache_manager_queue.arn}/SQSDefaultPolicy",
  "Statement": [
    {
      "Sid": "Sid1537946410847",
      "Effect": "Allow",
      "Principal": {
        "AWS": [
          "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/mart-role-${var.environment}"
        ]
      },
      "Action": "SQS:*",
      "Resource": "${aws_sqs_queue.apicenter_cache_manager_queue.arn}"
    }
  ]
}
POLICY
}