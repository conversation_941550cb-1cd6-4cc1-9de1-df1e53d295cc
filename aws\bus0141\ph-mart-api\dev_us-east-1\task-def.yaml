family: ph-mart-api-fargate-nlb-task-definition-dev
executionRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/mart-role-dev
taskRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/mart-role-dev
containerDefinitions:
  - name: ph-mart-api-fargate-container-dev
    image: <+variable.AWS_NONPROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/ph-mart-api-ecr:<+serviceVariables.ImageTag>
    cpu: 2048
    portMappings:
      - hostPort: 8080
        protocol: tcp
        containerPort: 8080
    essential: true
    environment:
      - name: ACTIVE_ENV
        value: dev
      - name: SERVICE_TYPE
        value: PHAPI
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /ecs/ph-mart-api-fargate-dev
        awslogs-region: us-east-1
        awslogs-stream-prefix: ecs
    secrets:
      - name: MARTGATEWAY_RDB_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/MART/RDB_PASSWORD"
      - name: MARTGATEWAY_REDSHIFT_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/LAKEHOUSE/ADMIN_PASSWORD"
      - name: MARTGATEWAY_UIM_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/MART/ENTITLEMENT_ADMIN_PASSWORD"
      - name: MARTGATEWAY_FIXED-INCOME_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/MART/FIXEDINCOME_DB_PASSWORD"

cpu: '2048'
memory: '4096'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>