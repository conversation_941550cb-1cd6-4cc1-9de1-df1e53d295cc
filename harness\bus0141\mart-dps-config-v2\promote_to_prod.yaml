name: Promote Artifacts from dev to prod bucket
items:
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: mart-dps-config/{{sourceVersion}}/dps-config.zip
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: mart-dps-config/{{targetVersion}}/dps-config.zip
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-prod-us-west-2
        key: mart-dps-config/{{targetVersion}}/dps-config.zip
      - accountId: "************"
        region: eu-west-1
        bucket: velo-deploy-prod-eu-west-1
        key: mart-dps-config/{{targetVersion}}/dps-config.zip

#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-prod-us-east-1
#        key: mart-dps-config/{{targetVersion}}/datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-prod-us-west-2
#        key: mart-dps-config/{{targetVersion}}/datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-prod-eu-west-1
#        key: mart-dps-config/{{targetVersion}}/datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/xoi_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-prod-us-east-1
#        key: mart-dps-config/{{targetVersion}}/xoi_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-prod-us-west-2
#        key: mart-dps-config/{{targetVersion}}/xoi_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-prod-eu-west-1
#        key: mart-dps-config/{{targetVersion}}/xoi_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/lh_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-prod-us-east-1
#        key: mart-dps-config/{{targetVersion}}/lh_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-prod-us-west-2
#        key: mart-dps-config/{{targetVersion}}/lh_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-prod-eu-west-1
#        key: mart-dps-config/{{targetVersion}}/lh_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/athena_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-prod-us-east-1
#        key: mart-dps-config/{{targetVersion}}/athena_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-prod-us-west-2
#        key: mart-dps-config/{{targetVersion}}/athena_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-prod-eu-west-1
#        key: mart-dps-config/{{targetVersion}}/athena_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/ds_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-prod-us-east-1
#        key: mart-dps-config/{{targetVersion}}/ds_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-prod-us-west-2
#        key: mart-dps-config/{{targetVersion}}/ds_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-prod-eu-west-1
#        key: mart-dps-config/{{targetVersion}}/ds_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/fi_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-prod-us-east-1
#        key: mart-dps-config/{{targetVersion}}/fi_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-prod-us-west-2
#        key: mart-dps-config/{{targetVersion}}/fi_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-prod-eu-west-1
#        key: mart-dps-config/{{targetVersion}}/fi_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/data-group.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-prod-us-east-1
#        key: mart-dps-config/{{targetVersion}}/data-group.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-prod-us-west-2
#        key: mart-dps-config/{{targetVersion}}/data-group.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-prod-eu-west-1
#        key: mart-dps-config/{{targetVersion}}/data-group.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/mapping.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-prod-us-east-1
#        key: mart-dps-config/{{targetVersion}}/mapping.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-prod-us-west-2
#        key: mart-dps-config/{{targetVersion}}/mapping.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-prod-eu-west-1
#        key: mart-dps-config/{{targetVersion}}/mapping.xml
#
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/rdb/{{sourceVersion}}/rdb_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-prod-us-east-1
#        key: mart-dps-config/rdb/{{targetVersion}}/rdb_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-prod-us-west-2
#        key: mart-dps-config/rdb/{{targetVersion}}/rdb_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-prod-eu-west-1
#        key: mart-dps-config/rdb/{{targetVersion}}/rdb_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/rdb/{{sourceVersion}}/rdb_direct.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-prod-us-east-1
#        key: mart-dps-config/rdb/{{targetVersion}}/rdb_direct.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-prod-us-west-2
#        key: mart-dps-config/rdb/{{targetVersion}}/rdb_direct.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-prod-eu-west-1
#        key: mart-dps-config/rdb/{{targetVersion}}/rdb_direct.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/view/{{sourceVersion}}/datapoints_view.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-prod-us-east-1
#        key: mart-dps-config/view/{{targetVersion}}/datapoints_view.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-prod-us-west-2
#        key: mart-dps-config/view/{{targetVersion}}/datapoints_view.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-prod-eu-west-1
#        key: mart-dps-config/view/{{targetVersion}}/datapoints_view.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/view/{{sourceVersion}}/direct_datapoints_view.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-prod-us-east-1
#        key: mart-dps-config/view/{{targetVersion}}/direct_datapoints_view.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-prod-us-west-2
#        key: mart-dps-config/view/{{targetVersion}}/direct_datapoints_view.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-prod-eu-west-1
#        key: mart-dps-config/view/{{targetVersion}}/direct_datapoints_view.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/datapoint_whitelist.txt
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-prod-us-east-1
#        key: mart-dps-config/{{targetVersion}}/datapoint_whitelist.txt
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-prod-us-west-2
#        key: mart-dps-config/{{targetVersion}}/datapoint_whitelist.txt
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-prod-eu-west-1
#        key: mart-dps-config/{{targetVersion}}/datapoint_whitelist.txt
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/equity_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-prod-us-east-1
#        key: mart-dps-config/{{targetVersion}}/equity_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-prod-us-west-2
#        key: mart-dps-config/{{targetVersion}}/equity_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-prod-eu-west-1
#        key: mart-dps-config/{{targetVersion}}/equity_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/code_mappings.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-prod-us-east-1
#        key: mart-dps-config/{{targetVersion}}/code_mappings.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-prod-us-west-2
#        key: mart-dps-config/{{targetVersion}}/code_mappings.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-prod-eu-west-1
#        key: mart-dps-config/{{targetVersion}}/code_mappings.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/calculations.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-prod-us-east-1
#        key: mart-dps-config/{{targetVersion}}/calculations.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-prod-us-west-2
#        key: mart-dps-config/{{targetVersion}}/calculations.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-prod-eu-west-1
#        key: mart-dps-config/{{targetVersion}}/calculations.xml
