resource "aws_elasticache_replication_group" "apicenter_cache_cluster" {
  replication_group_id       = "apicenter-cache-v7-cluster-${var.environment}"
  description                = "APICenter Application Cache"
  node_type                  = var.cache_node_type
  port                       = 6379
  engine                     = "redis"
  engine_version             = "7.0"
  parameter_group_name       = "default.redis7.cluster.on"
  automatic_failover_enabled = true
  subnet_group_name          = "private-az-subnets"
  security_group_ids         = flatten([
    module.region_data.security_group_private_web,
    module.region_data.security_group_private_app
  ])
  replicas_per_node_group = 0
  num_node_groups         = var.cache_num_node_groups
  tags                    = local.tags
}
