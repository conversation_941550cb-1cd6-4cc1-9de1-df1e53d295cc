module "apicenter-ecr" {
  source            = "./../../../modules/ecr_repo"
  count             = local.is_dr ? 0 : 1
  repository_name   = "apicenter-ecr"
  create_in_dr      = true
  default_tag_list  = merge(local.tags,
    {
      SERVICEID   = "ts00057"
    })
  providers = {
    aws.dr = aws.dr
  }
}

module "apidata-ecr" {
  source            = "./../../../modules/ecr_repo"
  count             = local.is_dr ? 0 : 1
  repository_name   = "apidata-ecr"
  create_in_dr      = true
  default_tag_list  = merge(local.tags,
    {
      SERVICEID   = "ts00057"
    })
  providers = {
    aws.dr = aws.dr
  }
}

module "apidata_v1-ecr" {
  source            = "./../../../modules/ecr_repo"
  count             = local.is_dr ? 0 : 1
  repository_name   = "apidata_v1-ecr"
  create_in_dr      = true
  default_tag_list  = merge(local.tags,
    {
      SERVICEID   = "ts00057"
    })
  providers = {
    aws.dr = aws.dr
  }
}

module "apicenter-entitlement-ecr" {
  source            = "./../../../modules/ecr_repo"
  count             = local.is_dr ? 0 : 1
  repository_name   = "apicenter-entitlement-ecr"
  create_in_dr      = true
  default_tag_list  = merge(local.tags,
    {
      SERVICEID   = "ts00057"
    })
  providers = {
    aws.dr = aws.dr
  }
}

module "apicenter-backend-ecr" {
  source            = "./../../../modules/ecr_repo"
  count             = local.is_dr ? 0 : 1
  repository_name   = "apicenter-backend-ecr"
  create_in_dr      = true
  default_tag_list  = merge(local.tags,
    {
      SERVICEID   = "ts00057"
    })
  providers = {
    aws.dr = aws.dr
  }
}