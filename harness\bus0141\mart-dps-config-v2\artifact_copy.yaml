name: Promote Artifacts to versioned bucket
items:
  # New Approach to Packaging datapoint configuration as a single zip file
  # Individual file no longer need to be registered
  - sourceFile: dps-config.zip
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: mart-dps-config/{{version}}/dps-config.zip
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-nonprod-us-west-2
        key: mart-dps-config/{{version}}/dps-config.zip
      - accountId: "************"
        region: eu-west-1
        bucket: velo-deploy-nonprod-eu-west-1
        key: mart-dps-config/{{version}}/dps-config.zip

#  - sourceFile: datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-nonprod-us-east-1
#        key: mart-dps-config/{{version}}/datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-nonprod-us-west-2
#        key: mart-dps-config/{{version}}/datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-nonprod-eu-west-1
#        key: mart-dps-config/{{version}}/datapoints.xml
#
#  - sourceFile: xoi_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-nonprod-us-east-1
#        key: mart-dps-config/{{version}}/xoi_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-nonprod-us-west-2
#        key: mart-dps-config/{{version}}/xoi_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-nonprod-eu-west-1
#        key: mart-dps-config/{{version}}/xoi_datapoints.xml
#
#  - sourceFile: lh_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-nonprod-us-east-1
#        key: mart-dps-config/{{version}}/lh_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-nonprod-us-west-2
#        key: mart-dps-config/{{version}}/lh_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-nonprod-eu-west-1
#        key: mart-dps-config/{{version}}/lh_datapoints.xml
#
#  - sourceFile: athena_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-nonprod-us-east-1
#        key: mart-dps-config/{{version}}/athena_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-nonprod-us-west-2
#        key: mart-dps-config/{{version}}/athena_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-nonprod-eu-west-1
#        key: mart-dps-config/{{version}}/athena_datapoints.xml
#
#  - sourceFile: ds_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-nonprod-us-east-1
#        key: mart-dps-config/{{version}}/ds_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-nonprod-us-west-2
#        key: mart-dps-config/{{version}}/ds_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-nonprod-eu-west-1
#        key: mart-dps-config/{{version}}/ds_datapoints.xml
#
#  - sourceFile: fi_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-nonprod-us-east-1
#        key: mart-dps-config/{{version}}/fi_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-nonprod-us-west-2
#        key: mart-dps-config/{{version}}/fi_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-nonprod-eu-west-1
#        key: mart-dps-config/{{version}}/fi_datapoints.xml
#
#  - sourceFile: data-group.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-nonprod-us-east-1
#        key: mart-dps-config/{{version}}/data-group.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-nonprod-us-west-2
#        key: mart-dps-config/{{version}}/data-group.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-nonprod-eu-west-1
#        key: mart-dps-config/{{version}}/data-group.xml
#
#  - sourceFile: mapping.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-nonprod-us-east-1
#        key: mart-dps-config/{{version}}/mapping.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-nonprod-us-west-2
#        key: mart-dps-config/{{version}}/mapping.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-nonprod-eu-west-1
#        key: mart-dps-config/{{version}}/mapping.xml
#
#  - sourceFile: rdb/rdb_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-nonprod-us-east-1
#        key: mart-dps-config/rdb/{{version}}/rdb_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-nonprod-us-west-2
#        key: mart-dps-config/rdb/{{version}}/rdb_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-nonprod-eu-west-1
#        key: mart-dps-config/rdb/{{version}}/rdb_datapoints.xml
#
#  - sourceFile: rdb/rdb_direct.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-nonprod-us-east-1
#        key: mart-dps-config/rdb/{{version}}/rdb_direct.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-nonprod-us-west-2
#        key: mart-dps-config/rdb/{{version}}/rdb_direct.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-nonprod-eu-west-1
#        key: mart-dps-config/rdb/{{version}}/rdb_direct.xml
#
#  - sourceFile: view/datapoints_view.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-nonprod-us-east-1
#        key: mart-dps-config/view/{{version}}/datapoints_view.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-nonprod-us-west-2
#        key: mart-dps-config/view/{{version}}/datapoints_view.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-nonprod-eu-west-1
#        key: mart-dps-config/view/{{version}}/datapoints_view.xml
#
#  - sourceFile: view/direct_datapoints_view.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-nonprod-us-east-1
#        key: mart-dps-config/view/{{version}}/direct_datapoints_view.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-nonprod-us-west-2
#        key: mart-dps-config/view/{{version}}/direct_datapoints_view.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-nonprod-eu-west-1
#        key: mart-dps-config/view/{{version}}/direct_datapoints_view.xml
#
#  - sourceFile: datapoint_whitelist.txt
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-nonprod-us-east-1
#        key: mart-dps-config/{{version}}/datapoint_whitelist.txt
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-nonprod-us-west-2
#        key: mart-dps-config/{{version}}/datapoint_whitelist.txt
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-nonprod-eu-west-1
#        key: mart-dps-config/{{version}}/datapoint_whitelist.txt
#
#  - sourceFile: equity_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-nonprod-us-east-1
#        key: mart-dps-config/{{version}}/equity_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-nonprod-us-west-2
#        key: mart-dps-config/{{version}}/equity_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-nonprod-eu-west-1
#        key: mart-dps-config/{{version}}/equity_datapoints.xml
#
#  - sourceFile: code_mappings.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-nonprod-us-east-1
#        key: mart-dps-config/{{version}}/code_mappings.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-nonprod-us-west-2
#        key: mart-dps-config/{{version}}/code_mappings.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-nonprod-eu-west-1
#        key: mart-dps-config/{{version}}/code_mappings.xml
#
#  - sourceFile: calculations.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: velo-deploy-nonprod-us-east-1
#        key: mart-dps-config/{{version}}/calculations.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: velo-deploy-nonprod-us-west-2
#        key: mart-dps-config/{{version}}/calculations.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: velo-deploy-nonprod-eu-west-1
#        key: mart-dps-config/{{version}}/calculations.xml