[
  {
    "name": "${container_name}",
    "essential": true,
    "image": "${image}",
    "cpu": ${cpu},
    "memoryReservation": ${memoryReservation},
    "readonlyRootFilesystem": false,
    "command": ${command},
    "mountPoints": [],
    "volumesFrom": [],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "${log_group}",
        "awslogs-region": "${region}",
        "awslogs-stream-prefix": "${env}"
      }
    },
    "environment": [
      {"name": "JAVA_OPTS","value": "${java_opts}"},
      {"naME": "APP_NAME","VALUE": "${app_name}"}
    ]
  }
]