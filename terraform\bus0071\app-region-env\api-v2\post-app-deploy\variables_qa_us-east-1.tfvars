region="us-east-1"
role_to_assume="arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment="qa"
environmentForTagging="qa"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################
app_env         = "qa"

min_capacity    = 0
max_capacity    = 1
create_resource = 1

tags = {
  ENVIRONMENT    = "qa"
  ORG            = "dataservice"
  MANAGED        = "terraform"
  PID            = "PID0497"
  SERVICE        = "Equity API V2"
  TID            = "EQAPI"
  SERVICEID      = "ts00804"
  BUSID          = "bus0071"
  FUNCTION       = "app"
  Name           = "ts00804-prd-eqapi-restful-api"
}