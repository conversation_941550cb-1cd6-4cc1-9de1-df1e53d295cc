deploy_role_name: mstar-engr-cross-account-deploy

# ECS Services
ecs_services:
  - service_name: velo-standalone-service-stg   # Website
    cluster_name: velo-stand-alone-cluster-stg
    autoscaling: true
    min_capacity: 1
  - service_name: velo-schedule-service-stg  # Scheduler
    cluster_name: velo-stand-alone-cluster-stg
    autoscaling: true
    min_capacity: 1
  - service_name: velo-feed-api-service-stg  # External API
    cluster_name: velo-stand-alone-cluster-stg
    autoscaling: true
    min_capacity: 1
  - service_name: mart-monitor-service-stg  # Mart Monitor
    cluster_name: velo-stand-alone-cluster-stg
    autoscaling: false
    min_capacity: 1

# SSM Parameters
ssm:
  source_region: us-east-1
  sync_tag_key: SyncAcrossRegions

# Schedulers Configuration
scheduler:
  - template:
      location: 'templates/sqs_schedule_switch.json.j2'
      variables:
        message_target_group: velo
        url: https://sqs.us-east-1.amazonaws.com/************/velo-scheduler-sqs-stg.fifo
  - template:
      location: 'templates/sqs_schedule_switch.json.j2'
      variables:
        message_target_group: fdo
        url: https://sqs.us-east-1.amazonaws.com/************/velo-scheduler-sqs-stg.fifo

# Elasticsearch Configuration
es:
  - domain_name: stg-change-notification
    template:
      location: 'templates/es-change-notification.json.j2'
      variables:
        domain_name: stg-change-notification
        access_policy: '{\"Version\": \"2012-10-17\",\"Statement\": [{\"Effect\": \"Allow\",\"Principal\": {\"AWS\": \"*\"},\"Action\": \"es:*\",\"Resource\": \"arn:aws:es:us-west-2:************:domain/stg-change-notification/*\"}]}'
        subnets:
          - subnet-03d560dd0e5253099
          - subnet-0cfbd3bd59d7c2cad
          - subnet-084b8c666e71a339f
        security_group_ids:
          - sg-03aae3fda738ccd3f
        tags:
          TID: DATAAC
          PID: PID0574
          SERVICEID: ts01004
    route53:
      template:
        location: 'templates/route53/change-notification.json.j2'
        variables:
          zone_id: Z1763QDC3XWVS7
          domain_name: datasvc-change-notification-stg-dr.date7ebe.easn.morningstar.com
    snapshot:
      lambda: backup-snapshot-stg
      restore:
        payload:
          task: changeNotificationES
          action: restore_change_notification
      create:
        payload:
          task: changeNotificationES
  - domain_name: s-mart-stg-search
    snapshot:
      lambda: backup-snapshot-stg
      restore:
        payload:
          task: martFeedES
          action: restore
          es_name: changelog
          es_restore_snapshot_name: latest
  - domain_name: mart-changelog-stg
    snapshot:
      lambda: backup-snapshot-stg
      restore:
        payload:
          task: martFeedES
          action: restore
          es_name: martES
          es_restore_snapshot_name: latest

# Route53 Configuration
route53:
  zone_id: Z1763QDC3XWVS7
  records:
    - name: Website
      lb_name: velo-standalone-lb-stg
      template:
        location: 'templates/route53/website.json.j2'
        variables:
          identifier: stg
          domain_name: velo-standalone.date7ebe.easn.morningstar.com
    - name: External
      lb_name: velo-feed-api-lb-stg
      template:
        location: 'templates/route53/external.json.j2'
        variables:
          domain_name: velo-feed-api-stg.date7ebe.easn.morningstar.com
    - name: Monitor
      lb_name: velo-standalone-lb-stg
      template:
        location: 'templates/route53/monitor.json.j2'
        variables:
          identifier: stg-us-east-1
          domain_name: mart-monitor-stg.date7ebe.easn.morningstar.com

lambda:
  - name: standalone-velo-lambda-dfd-generator-stg
    environment:
      variables:
        - name: datasource_session_variable
          dr_testing_value: "&sessionVariables=aurora_replica_read_consistency=global"
          value: ""
  - name: standalone-velo-lambda-async-universe-list-stg
    environment:
      variables:
        - name: datasource_session_variable
          dr_testing_value: "&sessionVariables=aurora_replica_read_consistency=global"
          value: ""
  - name: standalone-velo-lambda-fdo-stg
    environment:
      variables:
        - name: datasource_session_variable
          dr_testing_value: "&sessionVariables=aurora_replica_read_consistency=global"
          value: ""
  - name: standalone-velo-lambda-feed-message-consumer-stg
    environment:
      variables:
        - name: datasource_session_variable
          dr_testing_value: "&sessionVariables=aurora_replica_read_consistency=global"
          value: ""
  - name: standalone-velo-lambda-notification-handler-stg
    environment:
      variables:
        - name: datasource_session_variable
          dr_testing_value: "&sessionVariables=aurora_replica_read_consistency=global"
          value: ""
  - name: velo-entitlement-sync-index-series-stg
    environment:
      variables:
        - name: datasource_session_variable
          dr_testing_value: "&sessionVariables=aurora_replica_read_consistency=global"
          value: ""
  - name: velo-disable-feed-expired-licenses-stg
    environment:
      variables:
        - name: datasource_session_variable
          dr_testing_value: "&sessionVariables=aurora_replica_read_consistency=global"
          value: ""