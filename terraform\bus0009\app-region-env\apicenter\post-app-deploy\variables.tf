variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The environment"
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

variable "name" {}

variable "load_balancer_name" {}

variable "route53_zone_name" {}

variable "subdomain_name" {}

variable "route53_weight" {
  default = 1
}

variable "is_dr" {
  default = false
}




