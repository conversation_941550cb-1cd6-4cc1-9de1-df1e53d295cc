family: fundapi-v1-task-prod
executionRoleArn: arn:aws:iam::<+variable.AWS_PROD_ACCOUNT_ID>:role/mart-role-prod
taskRoleArn: arn:aws:iam::<+variable.AWS_PROD_ACCOUNT_ID>:role/mart-role-prod
containerDefinitions:
  - name: fundapi-v1-container-prod
    image: <+variable.AWS_PROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/apidata_v1-ecr:<+serviceVariables.ImageTag>
    cpu: 4096
    memory: 12288
    portMappings:
      - hostPort: 8080
        protocol: tcp
        containerPort: 8080
    essential: true
    environment:
      - name: ACTIVE_ENV
        value: "prod-dr"
      - name: MEMORY
        value: "9g"
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /aws/ecs/fundapi-v1-prod
        awslogs-region: us-west-2
        awslogs-stream-prefix: ecs
cpu: '4096'
memory: '12288'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: NAME
    value: fundapi-prod
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
  - key: PID
    value: PID0029
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
