name: Copy Artifacts to S3
items:
  - sourceFile: dfd/target/apicenter-dfd.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: apicenter-dfd/{{version}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-nonprod-us-west-2
        key: apicenter-dfd/{{version}}/app.jar
  - sourceFile: async-api/target/apicenter-async-api.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: apicenter-async-api/{{version}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-nonprod-us-west-2
        key: apicenter-async-api/{{version}}/app.jar
  - sourceFile: cache-manager/target/apicenter-cache-manager.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: apicenter-cache-manager/{{version}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-nonprod-us-west-2
        key: apicenter-cache-manager/{{version}}/app.jar