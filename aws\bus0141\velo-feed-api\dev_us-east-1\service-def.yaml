launchType: FARGATE
serviceName: velo-feed-api-service-dev
desiredCount: 1
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-08a83e28c2216dc3c # private_web
      - sg-03afef83e7d6959c8 # private_app
      - sg-0fd2a98817ac752f7 # private_db
    subnets:
      - subnet-08f0ece89d42f7d73 # us-east-1c-private
      - subnet-0a1c0ff43ccc5652c # us-east-1b-private
      - subnet-00fa9e860f858a4a2 # us-east-1a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 50

healthCheckGracePeriodSeconds: 10

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: velo-feed-api-container-dev
    containerPort: 8080

enableExecuteCommand: true
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION