resource "aws_cloudwatch_log_group" "entitlement_log_group" {
  name              = "/aws/ecs/${var.application_name}-${var.environment}"
  retention_in_days = var.log_retention_days
  tags              = local.tags
}

resource "aws_cloudwatch_log_subscription_filter" "entitlement_log_subscription_filter" {
  count           = var.kinesis_stream_count
  name            = "${var.application_name}-log-subscription-${var.environment}"
  log_group_name  = aws_cloudwatch_log_group.entitlement_log_group.name
  filter_pattern  = ""
  destination_arn = data.aws_kinesis_stream.stream[count.index].arn
  role_arn        = data.aws_iam_role.log_role.arn
}

