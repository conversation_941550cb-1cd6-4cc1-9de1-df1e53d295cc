family: asyncdata-feed-worker
executionRoleArn: arn:aws:iam::<+variable.AWS_PROD_ACCOUNT_ID>:role/PROD-ecs-task-execution-role
taskRoleArn: arn:aws:iam::<+variable.AWS_PROD_ACCOUNT_ID>:role/asyncdata/ecs-asyncdata-feed-worker
containerDefinitions:
  - name: asyncdata-feed-worker
    image: <+variable.AWS_PROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/delta-feed-builder-ecr:<+serviceVariables.ImageTag>
    cpu: 4096
    memory: 8192
    essential: true
    environment:
      - name: MSTAR_ENV
        value: prod
      - name: JAVA_TOOL_OPTIONS
        value: -Xms6G -Xmx6G -Dserde_codec_tag=deflate -XX:+UseG1GC -XX:MaxGCPauseMillis=3000 -XX:+ExitOnOutOfMemoryError -XX:+UnlockExperimentalVMOptions -XX:+UseContainerSupport -XX:+UseStringDeduplication -XX:MaxMetaspaceSize=100m
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /ecs/asyncdata-feed-worker
        awslogs-region: us-east-1
        awslogs-stream-prefix: asyncdata-feed-worker

cpu: '4096'
memory: '8192'
ephemeralStorage:
  sizeInGiB: 100
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>