variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "tag_values" {
  default = {
    MANAGED = "terraform"
    TID     = "DATAAC"
  }

  description = "tags"
}

variable "serviceIdMap" {
  type = map(string)
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

variable "cache_name" {
  type = string
}

variable "enable_vo_alert" {
  type    = bool
  default = false
}

locals {
  tags = {
    MANAGED     = "terraform"
    TID         = "DATAAC"
    ENVIRONMENT = var.environmentForTagging
    SERVICEID   = "ts00057"
  }
}