region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "dev"
environmentForTagging = "dev"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

vpc_id                   = "vpc-036c0231856bf15b6"
deregistration_delay     = 20
listener_certificate_arn = "arn:aws:acm:us-east-1:************:certificate/310385b8-db4b-46c0-b3e6-c9e08ead2540"
listener_priority        = 110
host_header = ["fundapi-dev.date7ebe.easn.morningstar.com", "api-qa.morningstar.com"]
kinesis_stream_name      = "mstar-kinesis-dataac-non-prod-splunk-us-east-1"
log_retention_days       = 1