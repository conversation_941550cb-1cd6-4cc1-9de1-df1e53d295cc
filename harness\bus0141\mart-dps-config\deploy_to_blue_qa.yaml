name: Copy Artifacts from versioned to qa bucket
items:
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: mart-dps-config/{{sourceVersion}}/dps-config.zip
      expandZip: true
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: mart-data-qa
        key: config/
      # Blue Environment for B/G Deployment
      - accountId: "************"
        region: us-east-1
        bucket: mart-data-qa
        # This should be blue since we aren't using a b/g deployment style in qa
        key: blue-env-config/
