module "eqapi-onetime-dump-batch-job" {
  source                               = "./../../../modules/batch"
  launch_template_name                 = "${var.tsid}-${var.environmentForTagging}-${var.function}-onetime-dump-template"
  batch_compute_environment_name       = "${var.tsid}-${var.environmentForTagging}-${var.function}-onetime-dump-ce"
  batch_job_definition_name            = "${var.tsid}-${var.environmentForTagging}-${var.function}-onetime-dump-batch"
  batch_job_queue_name                 = "${var.tsid}-${var.environmentForTagging}-${var.function}-onetime-dump-queue"
  batch_role_arn                       = data.aws_iam_role.eqapi-onetime-dump-role.arn
  batch_instance_profile_arn           = data.aws_iam_instance_profile.eqapi-onetime-dump-instance-profile.arn
  instance_type                        = var.batch_instance_type
  instance_volume_type                 = var.batch_instance_volume_type
  instance_volume_size                 = var.batch_instance_volume_size
  instance_volume_iops                 = var.batch_instance_volume_iops

  security_groups                      = ["private_app","private_web","private_ftp_morningstar_com"]
  subnets_ids                          = var.subnets_ids
  vpc_tag                              = var.vpc_tag
  region                               = var.region
  app_env                              = var.app_env

  create_resource                      = var.create_resource
  tags                                 = var.batch_tags
  environment                          = var.environmentForTagging
  group                                = var.group
  image_tag                            = var.release_version
  product                              = var.product

  ami_id                               = var.ami_id
  ec2_keypair                          = var.ec2_keypair
  log_group                            = var.environmentForTagging == "prd" ? "/aws/batch/operations/${var.environmentForTagging}-${var.group}-onetime-dump" : "/aws/batch/${var.environmentForTagging}-operations/${var.environmentForTagging}-${var.group}-onetime-dump"
  java_opts                            = var.java_opts_batch
  app_name                             = "onetime"
}