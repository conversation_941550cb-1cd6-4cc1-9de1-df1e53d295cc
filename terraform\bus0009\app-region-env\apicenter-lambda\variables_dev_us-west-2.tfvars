region                = "us-west-2"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "dev"
environmentForTagging = "dev"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

account_env                           = "nonprod"
deploy_max                            = "10"
deploy_min                            = "0"
reserved_lambda_concurrent_executions = "30"
application_profile                   = "dev_dr"
dfd_lambda_access_group = [
  "arn:aws:iam::************:role/velo-stand-alone-dev",
  "arn:aws:iam::************:role/velo-stand-alone-stg",
  "arn:aws:iam::************:role/mart-role-dev",
  "arn:aws:iam::************:role/mart-role-stg"
]
log_retention_days = 1