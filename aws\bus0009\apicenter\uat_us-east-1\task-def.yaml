family: apicenter-task-uat
executionRoleArn: arn:aws:iam::<+variable.AWS_PROD_ACCOUNT_ID>:role/apicenter-role-uat
taskRoleArn: arn:aws:iam::<+variable.AWS_PROD_ACCOUNT_ID>:role/apicenter-role-uat
containerDefinitions:
  - name: apicenter-container-uat
    image: <+variable.AWS_PROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/apicenter-ecr:<+serviceVariables.ImageTag>
    cpu: 512
    memory: 3072
    portMappings:
      - hostPort: 8080
        protocol: tcp
        containerPort: 8080
    essential: true
    environment:
      - name: ACTIVE_ENV
        value: "uat"
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /aws/ecs/apicenter-uat
        awslogs-region: us-east-1
        awslogs-stream-prefix: ecs
cpu: '512'
memory: '3072'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: NAME
    value: apicenter-uat
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
  - key: PID
    value: PID0029
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
