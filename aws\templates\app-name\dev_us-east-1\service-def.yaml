launchType: FARGATE
serviceName:
desiredCount: 1
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-03afef83e7d6959c8 # private_app
      - sg-08a83e28c2216dc3c # private_web
      - sg-0fd2a98817ac752f7 # private_db
      - sg-009b17a4e9b81a5f0 # private_internal_email_morningstar
    subnets:
      - subnet-08f0ece89d42f7d73
      - subnet-0a1c0ff43ccc5652c
      - subnet-00fa9e860f858a4a2
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 50

loadBalancers:
  - targetGroupArn: arn:aws:elasticloadbalancing:us-east-1:270863951168:targetgroup/mart-api-tg-nlb-dev/a1bbcccaff8ac19b
    containerName: mart-api-fargate-container-dev
    containerPort: 8080