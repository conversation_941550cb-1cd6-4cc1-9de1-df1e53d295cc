name: Copy Artifacts from non-prod to prod account
items:
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: apicenter-dfd/{{sourceVersion}}/app.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: apicenter-dfd/{{targetVersion}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-prod-us-west-2
        key: apicenter-dfd/{{targetVersion}}/app.jar
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: apicenter-async-api/{{sourceVersion}}/app.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: apicenter-async-api/{{targetVersion}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-prod-us-west-2
        key: apicenter-async-api/{{targetVersion}}/app.jar
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: apicenter-cache-manager/{{sourceVersion}}/app.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: apicenter-cache-manager/{{targetVersion}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-prod-us-west-2
        key: apicenter-cache-manager/{{targetVersion}}/app.jar