name: Copy Artifacts to S3
items:
  - sourceFile: function.zip
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: mart-aws-loader-trigger/{{version}}/function.zip
  - sourceFile: riskModelAthenaMappingTriggerFunction.zip
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: riskmodel-athena-mapping-trigger/{{version}}/riskModelAthenaMappingTriggerFunction.zip
