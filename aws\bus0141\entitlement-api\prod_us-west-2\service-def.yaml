launchType: FARGATE
serviceName: prod-entitlement-api-service-nlb
desiredCount: 0
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0c62d1668d1a136cd # private_app
      - sg-01116f50a47ead028 # private_web
      - sg-04369231c2b3296ce # private_db
    subnets:
      - subnet-001ccb29fa360fac8 # us-east-1c-private
      - subnet-04b0eee56af730a20 # us-east-1b-private
      - subnet-00a0c2588e6af526c # us-east-1a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent: 200
  minimumHealthyPercent: 50

healthCheckGracePeriodSeconds: 300

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: prod-entitlement-api
    containerPort: 8443

tags:
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: ORG
    value: <+serviceVariables.ORG>
  - key: TID
    value: <+variable.TID>
  - key: PID
    value: <+serviceVariables.PID>
  - key: MANAGED
    value: <+serviceVariables.MANAGED>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>

propagateTags: TASK_DEFINITION