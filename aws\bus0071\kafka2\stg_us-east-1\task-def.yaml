family: ts00804-qa-app-eqapi-ongoing-dump-kafka2
executionRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/ts00804-stg-us-east-1-app-ongoing-dump-role
taskRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/ts00804-stg-us-east-1-app-ongoing-dump-role
containerDefinitions:
  - name: ts00804-stg-app-eqapi-ongoing-dump-kafka2-container
    image: <+variable.AWS_NONPROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/eqapi/api-v2:<+serviceVariables.ImageTag>
    cpu: 2048
    essential: true
    command: 
      - "--spring.profiles.active=stg"
      - "--resumeTopic=default"
      - "--workflows=ged_equitydata_income_statement_cpms,ged_equitydata_share_class_information,ged_equitydata_earning_report_cpms,ged_equitydata_debt_maturity_schedule,ged_equitydata_income_statement,ged_equitydata_cash_flow,ged_equitydata_earning_report,ged_equitydata_spin_off,ged_equitydata_stock_split,ged_equitydata_rights_issue,ged_equitydata_cash_dividend,ged_equitydata_capital_repayment,ged_equitydata_income_statement_calculations_credit_analysis,ged_equitydata_balance_sheet_calculations_credit_analysis,ged_equitydata_operating_metrics_industry_ratio,ged_equitydata_operating_metrics_income_statement,ged_equitydata_operating_metrics_balance_sheet,ged_equitydata_balance_sheet_supplemental,ged_equitydata_income_statement_supplemental,ged_equitydata_financial_ratio_supplemental,ged_equitydata_segment_data_supplemental,ged_equitydata_calculated_metrics,ged_equitydata_calculated_ratio,ged_equitydata_cash_flow_calculations,ged_equitydata_income_statement_calculations,ged_equitydata_cash_flow_calculations_per_share,ged_equitydata_enterprise_value_calculations,ged_equitydata_efficiency_ratios,ged_equitydata_leverage"
            
    environment:
      - name: JAVA_OPTS
        value: -XX:MaxRAMPercentage=80.0 -XX:MinRAMPercentage=40.0 -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC
      - name: APP_NAME
        value: ongoing
      - name: ENV
        value: staging
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /aws/ecs/stg-eqapi-ongoing-dump
        awslogs-region: <+infra.region>
        awslogs-stream-prefix: ecs
cpu: '2048'
memory: '7800'
requiresCompatibilities:
  - EC2
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>