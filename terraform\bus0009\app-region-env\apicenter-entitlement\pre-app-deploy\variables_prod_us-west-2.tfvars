region                = "us-west-2"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "prod"
environmentForTagging = "prd"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

vpc_id               = "vpc-0983ebdf6c6fe127b"
deregistration_delay = 20
listener_priority    = 200
host_header = ["apicenter-backend-prod.dat688b3.eas.morningstar.com", "apicentersupport.morningstar.com"]
listener_certificate_arn = "arn:aws:acm:us-west-2:************:certificate/d1a24a1f-03bd-4420-988f-13eed28e7082"  # *.dat688b3.eas.morningstar.com
kinesis_stream_name  = "mstar-kinesis-dataac-prod-splunk-us-west-2"
log_retention_days   = 3