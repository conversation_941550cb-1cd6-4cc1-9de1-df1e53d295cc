family: fundapi-v1-task-dev
executionRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/mart-role-dev
taskRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/mart-role-dev
containerDefinitions:
  - name: fundapi-v1-container-dev
    image: <+variable.AWS_NONPROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/apidata_v1-ecr:<+serviceVariables.ImageTag>
    cpu: 1024
    memory: 3072
    portMappings:
      - hostPort: 8080
        protocol: tcp
        containerPort: 8080
    essential: true
    environment:
      - name: ACTIVE_ENV
        value: "dev-dr"
      - name: MEMORY
        value: "2g"
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /aws/ecs/fundapi-v1-dev
        awslogs-region: us-west-2
        awslogs-stream-prefix: ecs
cpu: '1024'
memory: '3072'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: NAME
    value: fundapi-dev
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
  - key: PID
    value: PID0029
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
