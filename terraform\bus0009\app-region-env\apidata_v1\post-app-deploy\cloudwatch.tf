resource "aws_cloudwatch_metric_alarm" "fund_api_service_alarm_high" {
  alarm_name          = "${var.application_name}-cpu-alarm-high-${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = var.cloudwatch_evaluation_period
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = var.cloudwatch_period
  statistic           = "Average"
  threshold           = var.scale_out_threshold

  dimensions = {
    ClusterName = data.aws_ecs_cluster.fund_api_cluster.cluster_name
    ServiceName = data.aws_ecs_service.fund_api_service.service_name
  }

  alarm_description = "Alarm if service CPU too high"
  alarm_actions     = [aws_appautoscaling_policy.fund_api_service_scale_up_policy.arn]
  tags              = local.tags
}

resource "aws_cloudwatch_metric_alarm" "fund_api_service_alarm_normal" {
  alarm_name          = "${var.application_name}-cpu-alarm-back-to-normal-${var.environment}"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = var.cloudwatch_evaluation_period
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = var.cloudwatch_period
  statistic           = "Average"
  threshold           = var.scale_in_threshold

  dimensions = {
    ClusterName = data.aws_ecs_cluster.fund_api_cluster.cluster_name
    ServiceName = data.aws_ecs_service.fund_api_service.service_name
  }

  alarm_description = "Alarm if service CPU back to normal"
  alarm_actions     = [aws_appautoscaling_policy.fund_api_service_scale_down_policy.arn]
  tags              = local.tags
}

resource "aws_cloudwatch_metric_alarm" "fund_api_service_cpu_vo_alert" {
  alarm_name          = "${var.application_name}-cpu-over-90-percentage-alarm-${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = 120
  statistic           = "Average"
  threshold           = 90

  dimensions = {
    ClusterName = data.aws_ecs_cluster.fund_api_cluster.cluster_name
    ServiceName = data.aws_ecs_service.fund_api_service.service_name
  }

  alarm_description = "CPU Used % is more than 90 for at least 2 minutes"
  actions_enabled   = var.enable_vo_alert ? true : false
  alarm_actions     = var.enable_vo_alert ? [data.aws_sns_topic.apicenter_vo_alert[0].arn] : []
  ok_actions        = var.enable_vo_alert ? [data.aws_sns_topic.apicenter_vo_alert[0].arn] : []
  tags              = local.tags
}

resource "aws_cloudwatch_metric_alarm" "fund_api_service_memory_vo_alert" {
  alarm_name          = "${var.application_name}-memory-over-90-percentage-alarm-${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = 120
  statistic           = "Average"
  threshold           = 90

  dimensions = {
    ClusterName = data.aws_ecs_cluster.fund_api_cluster.cluster_name
    ServiceName = data.aws_ecs_service.fund_api_service.service_name
  }

  alarm_description = "Memory Used % is more than 90 for at least 2 minutes"
  actions_enabled   = var.enable_vo_alert ? true : false
  alarm_actions     = var.enable_vo_alert ? [data.aws_sns_topic.apicenter_vo_alert[0].arn] : []
  ok_actions        = var.enable_vo_alert ? [data.aws_sns_topic.apicenter_vo_alert[0].arn] : []
  tags              = local.tags
}