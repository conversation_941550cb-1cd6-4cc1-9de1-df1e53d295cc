family: essentials-image-loader-task-dev
executionRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/essentials-role-dev
taskRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/essentials-role-dev
containerDefinitions:
  - name: essentials-image-loader-container-dev
    image: <+variable.AWS_NONPROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/cloud-essentials-image-loader-ecr:<+serviceVariables.ImageTag>
    cpu: 256
    portMappings:
      - hostPort: 8080
        protocol: tcp
        containerPort: 8080
    essential: true
    environment:
      - name: ACTIVE_ENV
        value: dev
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /ecs/essentials-image-loader-task-dev
        awslogs-region: us-east-1
        awslogs-stream-prefix: ecs
cpu: '2048'
memory: '4096'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: NAME
    value: essentials-image-loader-management-dev
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
  - key: PID
    value: PID0029
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
