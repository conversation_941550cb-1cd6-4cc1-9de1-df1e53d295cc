deploy_role_name: mstar-engr-cross-account-deploy

ecs_services:
  - service_name: fundapi-v2-service-prod
    cluster_name: fundapi-cluster-prod
    autoscaling: true
    min_capacity: 1
  - service_name: fundapi-v1-service-prod
    cluster_name: fundapi-cluster-prod
    autoscaling: true
    min_capacity: 1
  - service_name: apicenter-service-prod
    cluster_name: fundapi-cluster-prod
    autoscaling: false
    min_capacity: 1
  - service_name: apicenter-backend-service-prod
    cluster_name: fundapi-cluster-prod
    autoscaling: false
    min_capacity: 1
  - service_name: apicenter-entitlement-service-prod
    cluster_name: fundapi-cluster-prod
    autoscaling: false
    min_capacity: 1

route53:
  zone_id: ZPHK6BJ6MT62F
  location: 'templates/route53.json.j2'
  domains:
    - domain: apicenter-prod.dat688b3.eas.morningstar.com
      records:
        - set_identifier: prod
          region: us-east-1
        - set_identifier: prod-dr
          region: us-west-2
    - domain: apicenter-backend-prod.dat688b3.eas.morningstar.com
      records:
        - set_identifier: prod
          region: us-east-1
        - set_identifier: prod-dr
          region: us-west-2
    - domain: fundapi-prod.dat688b3.eas.morningstar.com
      records:
        - set_identifier: prod
          region: us-east-1
        - set_identifier: prod-dr
          region: us-west-2