region                = "us-west-2"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "prod"
environmentForTagging = "prd"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

vpc_id               = "vpc-0983ebdf6c6fe127b"
deregistration_delay = 300
listener_certificate_arn = "arn:aws:acm:us-west-2:************:certificate/d1a24a1f-03bd-4420-988f-13eed28e7082"  # *.dat688b3.eas.morningstar.com
listener_priority    = 100
host_header = ["fundapi-prod.dat688b3.eas.morningstar.com", "api.morningstar.com"]
kinesis_stream_name  = "mstar-kinesis-dataac-prod-splunk-us-west-2"
log_retention_days   = 3