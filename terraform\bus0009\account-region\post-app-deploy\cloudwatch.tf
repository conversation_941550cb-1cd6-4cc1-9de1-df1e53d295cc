resource "aws_cloudwatch_metric_alarm" "apicenter_cache_warning_cpu_util_greater_than_75perc" {
  for_each            = data.aws_elasticache_replication_group.apicenter_cache.member_clusters
  alarm_name          = format("%s-%s-%s", each.key, "warning-cpu-util-greater-than-75perc", "${var.environment}")
  comparison_operator = "GreaterThanOrEqualToThreshold"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ElastiCache"
  period              = 300
  evaluation_periods  = 1
  statistic           = "Average"
  threshold           = 75

  dimensions = {
    CacheClusterId = each.key
  }

  alarm_description = "CPU Utilization of apicenter-application-cache is greater than 75%"
  actions_enabled   = var.enable_vo_alert ? true : false
  alarm_actions     = var.enable_vo_alert ? [data.aws_sns_topic.apicenter_vo_alert[0].arn] : []
  tags              = local.tags
}


resource "aws_cloudwatch_metric_alarm" "apicenter_cache_critical_cpu_util_greater_than_95perc" {
  for_each            = data.aws_elasticache_replication_group.apicenter_cache.member_clusters
  alarm_name          = format("%s-%s-%s", each.key, "critical-cpu-util-greater-than-95perc", var.environment)
  comparison_operator = "GreaterThanOrEqualToThreshold"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ElastiCache"
  period              = 300
  evaluation_periods  = 1
  statistic           = "Average"
  threshold           = 95

  dimensions = {
    CacheClusterId = each.key
  }

  alarm_description = "CPU Utilization of apicenter-application-cache is greater than 95%"
  actions_enabled   = var.enable_vo_alert ? true : false
  alarm_actions     = var.enable_vo_alert ? [data.aws_sns_topic.apicenter_vo_alert[0].arn] : []
  tags              = local.tags
}

resource "aws_cloudwatch_metric_alarm" "apicenter_cache_critical_memory_util_greater_than_95perc" {
  for_each            = data.aws_elasticache_replication_group.apicenter_cache.member_clusters
  alarm_name          = format("%s-%s-%s", each.key, "critical-db-memory-greater-than-95perc", var.environment)
  comparison_operator = "GreaterThanOrEqualToThreshold"
  metric_name         = "DatabaseMemoryUsagePercentage"
  namespace           = "AWS/ElastiCache"
  period              = 21600
  evaluation_periods  = 1
  statistic           = "Average"
  threshold           = 95

  dimensions = {
    CacheClusterId = each.key
  }

  alarm_description = "DB Memory Usage of apicenter-application-cache is greater than 95%"
  actions_enabled   = var.enable_vo_alert ? true : false
  alarm_actions     = var.enable_vo_alert ? [data.aws_sns_topic.apicenter_vo_alert[0].arn] : []
  tags              = local.tags
}
