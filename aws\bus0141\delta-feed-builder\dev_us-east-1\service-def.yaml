launchType: FARGATE
serviceName: dev-asyncdata-feed-worker
desiredCount: 2
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-03afef83e7d6959c8 # private_app
    subnets:
      - subnet-08f0ece89d42f7d73 # us-east-1c-private
      - subnet-0a1c0ff43ccc5652c # us-east-1b-private
      - subnet-00fa9e860f858a4a2 # us-east-1a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 100
tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION