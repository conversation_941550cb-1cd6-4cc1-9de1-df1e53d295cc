launchType: FARGATE
serviceName: velo-admin-service-stg
desiredCount: 1
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-03afef83e7d6959c8 # private_app
      - sg-08a83e28c2216dc3c # private_web
      - sg-0fd2a98817ac752f7 # private_db
    subnets:
      - subnet-08f0ece89d42f7d73 # us-east-1c-private
      - subnet-0a1c0ff43ccc5652c # us-east-1b-private
      - subnet-00fa9e860f858a4a2 # us-east-1a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent: 200
  minimumHealthyPercent: 50

healthCheckGracePeriodSeconds: 300

loadBalancers:
  - targetGroupArn: arn:aws:elasticloadbalancing:us-east-1:270863951168:targetgroup/velo-admin-target-group-stg/0daf423aa795dd6c
    containerName: velo-admin-container-stg
    containerPort: 8080

tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>

propagateTags: TASK_DEFINITION