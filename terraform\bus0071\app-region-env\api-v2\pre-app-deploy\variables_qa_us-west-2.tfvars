region="us-west-2"
role_to_assume="arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment="qa"
environmentForTagging="qa"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

account_id      = "************"
s3_bucket       = "qa-equity-mart"
app_env         = "qadr"
subnets_ids     = ["subnet-d20a5cff","subnet-9a91dcc1"]

log_group       = "/aws/ecs/qa-eqapi-ongoing-dump"
instance_type   = "t3.medium"
instance_size   = 1
ec2_keypair     = "keypair_gedf_non_prod_test"
task_cpu        = 2048
task_memory     = 948
s3_task_memory  = 4096
java_opts       = "-XX:MaxRAMPercentage=80.0 -XX:MinRAMPercentage=40.0 -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC"
create_resource = 1

create_resource_kafka1 = 0
create_resource_kafka2 = 0
create_resource_kafka3 = 0
create_resource_datalake1 = 1

resume_offset = "default"
minDlLoadDates = "default"

ami_id                        =  "ami-0bee5171eb1f564e1"
java_opts_batch               =  "-DOperationLogGroup=/aws/batch/qa-operations -DOperationLogStream=qa-eqapi-onetime-dump -Xmx24G -Xms24G -XX:+UseG1GC"
batch_instance_type           =  ["m5.2xlarge"]
batch_instance_volume_type    =  "io1"
batch_instance_volume_size    =  2000
batch_instance_volume_iops    =  10000

redis_node_type               = "cache.t3.small"
redis_node_num                = 1
redis_shard_num               = 1
redis_replicas_per_shard      = 0
redis_engine_version          = "6.2"
redis_parameter_group         = "default.redis6.x.cluster.on"

es_instance_type              = "t3.small.elasticsearch"
es_instance_size              = 1
es_volume_size                = 20

access_log_bucket          = "mstar-s3-datasvc-non-prod-splunk-us-east-1"
web_java_opts                  = "-Dnewrelic.environment=development -javaagent:/usr/local/newrelic/newrelic.jar -XX:MaxRAMPercentage=80.0 -XX:MinRAMPercentage=40.0 -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC"
desired_count              = 0
min_capacity               = 0
max_capacity               = 1
certificate_domain         = "*.datf01ae.easn.morningstar.com"
#https://docs.aws.amazon.com/zh_cn/AmazonECS/latest/developerguide/task_definition_parameters.html#task_size
web_task_cpu                   = 2048
web_task_mem                   = 4096
r53_routing_live_weightage = 1
alb_healthy_threshold      = 2
alb_unhealthy_threshold    = 2
alb_health_check_timeout   = 5
alb_health_check_path      = "/"
alb_health_check_interval  = 10

group1_workflows = [
  "ged_equitydata_dividend_residual_risk_and_return_sensitivity"
]

group2_workflows = [
]

group3_workflows = [
]

#tags
vpc_tag         = "datasvc-non-prod"
tags = {
  ENVIRONMENT    = "qa"
  ORG            = "dataservice"
  MANAGED        = "terraform"
  PID            = "PID0497"
  SERVICE        = "Equity API V2"
  TID            = "EQAPI"
  SERVICEID      = "ts00804"
  BUSID          = "bus0071"
  FUNCTION       = "app"
  Name           = "ts00804-qa-eqapi-ongoing-dump"
}
batch_tags = {
  ENVIRONMENT    = "qa"
  ORG            = "dataservice"
  MANAGED        = "terraform"
  PID            = "PID0497"
  SERVICE        = "Equity API V2"
  TID            = "EQAPI"
  SERVICEID      = "ts00804"
  BUSID          = "bus0071"
  FUNCTION       = "app"
  Name           = "ts00804-qa-eqapi-onetime-dump"
}
web_tags =  {
  ENVIRONMENT              =  "qa"
  ORG                      =  "dataservice"
  MANAGED                  =  "terraform"
  PID                      =  "PID0497"
  SERVICE                  =  "Equity API V2"
  TID                      =  "EQAPI"
  SERVICEID                =  "ts00804"
  BUSID                    =  "bus0071"
  FUNCTION                 =  "web"
}
esg_tags=[
  {
    key = "Name"
    value= "ts00804-qa-eqapi-ongoing-dump"
    propagate_at_launch = true
  },
  {
    key                 = "BUSID"
    value               = "bus0071"
    propagate_at_launch = true
  },
  {
    key                 = "ORG"
    value               = "dataservice"
    propagate_at_launch = true
  },
  {
    key                 = "SERVICEID"
    value               = "ts00804"
    propagate_at_launch = true
  },
  {
    key                 = "ENVIRONMENT"
    value               = "qa"
    propagate_at_launch = true
  },
  {
    key                 = "PID"
    value               = "PID0497"
    propagate_at_launch = true
  },
  {
    key                 = "SERVICE"
    value               = "Equity API V2"
    propagate_at_launch = true
  },
  {
    key                 = "MANAGED"
    value               = "terraform"
    propagate_at_launch = true
  },
  {
    key                 = "TID"
    value               = "EQAPI"
    propagate_at_launch = true
  },
  {
    key                 = "FUNCTION"
    value               = "app"
    propagate_at_launch = true
  }
]

#sns
sns_topic_name = "eqapi-ongoing-dump-operations-notification"
sns_topic_recepient = "<EMAIL>"
