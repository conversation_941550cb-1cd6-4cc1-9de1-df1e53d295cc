launchType: FARGATE
serviceName: velo-schedule-service-dev
desiredCount: 1
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-08a83e28c2216dc3c # private_web
      - sg-03afef83e7d6959c8 # private_app
      - sg-0fd2a98817ac752f7 # private_db
      - sg-03b5c5743e666390a # private_ses
    subnets:
      - subnet-08f0ece89d42f7d73 # us-east-1c-private
      - subnet-0a1c0ff43ccc5652c # us-east-1b-private
      - subnet-00fa9e860f858a4a2 # us-east-1a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 50
tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION
