launchType: FARGATE
serviceName: essentials-image-loader-service-prod
desiredCount: 0
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0c62d1668d1a136cd # private_app
      - sg-01116f50a47ead028 # private_web
      - sg-04369231c2b3296ce # private_db
      - sg-0ce1f197e624ca33f #private_postfeed
    subnets:
      - subnet-04b0eee56af730a20
      - subnet-00a0c2588e6af526c
      - subnet-001ccb29fa360fac8
    assignPublicIp: DISABLED
deploymentConfiguration:
  maximumPercent: 200
  minimumHealthyPercent: 50
healthCheckGracePeriodSeconds : 300
schedulingStrategy : REPLICA
loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: essentials-image-loader-container-prod
    containerPort: 8080
tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION
