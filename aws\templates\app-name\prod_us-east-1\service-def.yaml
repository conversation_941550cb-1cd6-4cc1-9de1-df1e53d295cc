launchType: FARGATE
serviceName:
desiredCount: 6
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0e7ef1010d6acddf8  #private_web
      - sg-0d788a43acb654693  #private_app
      - sg-078ee4651c9f093eb  #private_db
      - sg-05c2bbd42b8377962  #private_internal_email_morningstar
    subnets:
      - subnet-007aa27f3f50932a2
      - subnet-08f032a54afd01bcc
      - subnet-0adafb2337a7adb7a
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 50

loadBalancers:
  - targetGroupArn: arn:aws:elasticloadbalancing:us-east-1:921072466220:targetgroup/mart-api-tg-nlb-prod/47362a21ba274170
    containerName: mart-api-fargate-container-prod
    containerPort: 8080