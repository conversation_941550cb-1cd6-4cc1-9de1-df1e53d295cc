deploy_role_name: mstar-engr-cross-account-deploy

rds:
  - template:
      location: 'templates/rds.json.j2'
      variables:
        source_region: "us-east-1"
        instance_id: "essentials-prod"
        rds_instance_class: "db.t3.medium"
        vpc_security_group_ids:
          - sg-050353f75bf4110c3
          - sg-04369231c2b3296ce
        tags:
          ENVIRONMENT: PROD
          TID: DATAAC
          PID: PID0029
          SERVICEID: ts00669

ecs_services:
  - service_name: essentials-website-service-prod
    cluster_name: essentials-cluster-prod
    autoscaling: false
    min_capacity: 1
  - service_name: essentials-user-service-prod
    cluster_name: essentials-cluster-prod
    autoscaling: false
    min_capacity: 1
  - service_name: essentials-image-loader-service-prod
    cluster_name: essentials-cluster-prod
    autoscaling: false
    min_capacity: 1
  - service_name: essentials-imageapi-service-prod
    cluster_name: essentials-cluster-prod
    autoscaling: false
    min_capacity: 1

route53:
  zone_id: ZPHK6BJ6MT62F
  location: 'templates/route53.json.j2'
  domains:
    - domain: essentials-prod.dat688b3.eas.morningstar.com
      records:
        - set_identifier: prod
          region: us-east-1
        - set_identifier: prod-dr
          region: us-west-2
    - domain: essentials-user-management-prod.dat688b3.eas.morningstar.com
      records:
        - set_identifier: prod
          region: us-east-1
        - set_identifier: prod-dr
          region: us-west-2
    - domain: essentials-image-loader-prod.dat688b3.eas.morningstar.com
      records:
        - set_identifier: prod
          region: us-east-1
        - set_identifier: prod-dr
          region: us-west-2
    - domain: essentials-image-api-prod.dat688b3.eas.morningstar.com
      records:
        - set_identifier: prod
          region: us-east-1
        - set_identifier: prod-dr
          region: us-west-2