resource "aws_lb_target_group" "entitlement_lb_target_group" {
  name                 = "${var.application_name}-tg-${var.environment}"
  port                 = 80
  protocol             = "HTTP"
  target_type          = "ip"
  vpc_id               = var.vpc_id
  deregistration_delay = var.deregistration_delay
  tags                 = local.tags

  health_check {
    path              = "/entitlement/health"
    port              = var.host_port
    healthy_threshold = 3
    interval          = 30
    timeout           = 25
  }

  lifecycle {
    create_before_destroy = true
    ignore_changes        = [name]
  }

  depends_on = [data.aws_lb.apicenter_lb]
}

resource "aws_lb_listener_certificate" "entitlement_listener_certificate" {
  listener_arn    = data.aws_lb_listener.apicenter_lb_listener.arn
  certificate_arn = var.listener_certificate_arn
}

resource "aws_lb_listener_rule" "apicenter_lb_https_listener_rule" {
  listener_arn = data.aws_lb_listener.apicenter_lb_listener.arn
  priority     = var.listener_priority

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.entitlement_lb_target_group.id
  }
  condition {
    path_pattern {
      values = ["/entitlement/*"]
    }
  }
  condition {
    host_header {
      values = concat(var.host_header, [data.aws_lb.apicenter_lb.dns_name])
    }
  }
  depends_on   = [aws_lb_target_group.entitlement_lb_target_group]
}