launchType: FARGATE
serviceName: velo-feed-api-service-prod
desiredCount: 2
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0e7ef1010d6acddf8 # private_web
      - sg-0d788a43acb654693 # private_app
      - sg-078ee4651c9f093eb # private_db
    subnets:
      - subnet-007aa27f3f50932a2 # us-east-1c-private
      - subnet-08f032a54afd01bcc # us-east-1a-private
      - subnet-0adafb2337a7adb7a # us-east-1b-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 50

healthCheckGracePeriodSeconds: 10

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: velo-feed-api-container-prod
    containerPort: 8080

enableExecuteCommand: true
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION