name: Copy Artifacts from versioned to dev bucket
items:
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: mart-dps-config/{{sourceVersion}}/dps-config.zip
      expandZip: true
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: mart-data-dev
        key: config/
      - accountId: "************"
        region: us-west-2
        bucket: mart-data-dev-dr
        key: config/
      - accountId: "************"
        region: eu-west-1
        bucket: mart-data-dev-eu
        key: config/
      # Blue Environment for B/G Deployment
      - accountId: "************"
        region: us-east-1
        bucket: mart-data-dev
        key: blue-env-config/
      - accountId: "************"
        region: us-west-2
        bucket: mart-data-dev-dr
        key: blue-env-config/
      - accountId: "************"
        region: eu-west-1
        bucket: mart-data-dev-eu
        key: blue-env-config/

#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-dev
#        key: config/datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-dev-dr
#        key: config/datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-dev-eu
#        key: config/datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/xoi_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-dev
#        key: config/xoi_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-dev-dr
#        key: config/xoi_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-dev-eu
#        key: config/xoi_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/lh_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-dev
#        key: config/lh_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-dev-dr
#        key: config/lh_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-dev-eu
#        key: config/lh_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/athena_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-dev
#        key: config/athena_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-dev-dr
#        key: config/athena_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-dev-eu
#        key: config/athena_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/ds_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-dev
#        key: config/ds_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-dev-dr
#        key: config/ds_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-dev-eu
#        key: config/ds_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/fi_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-dev
#        key: config/fi_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-dev-dr
#        key: config/fi_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-dev-eu
#        key: config/fi_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/data-group.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-dev
#        key: config/data-group.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-dev-dr
#        key: config/data-group.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-dev-eu
#        key: config/data-group.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/mapping.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-dev
#        key: config/mapping.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-dev-dr
#        key: config/mapping.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-dev-eu
#        key: config/mapping.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/rdb/{{sourceVersion}}/rdb_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-dev
#        key: config/rdb/rdb_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-dev-dr
#        key: config/rdb/rdb_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-dev-eu
#        key: config/rdb/rdb_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/rdb/{{sourceVersion}}/rdb_direct.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-dev
#        key: config/rdb/rdb_direct.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-dev-dr
#        key: config/rdb/rdb_direct.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-dev-eu
#        key: config/rdb/rdb_direct.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/view/{{sourceVersion}}/datapoints_view.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-dev
#        key: config/view/datapoints_view.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-dev-dr
#        key: config/view/datapoints_view.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-dev-eu
#        key: config/view/datapoints_view.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/view/{{sourceVersion}}/direct_datapoints_view.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-dev
#        key: config/view/direct_datapoints_view.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-dev-dr
#        key: config/view/direct_datapoints_view.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-dev-eu
#        key: config/view/direct_datapoints_view.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/datapoint_whitelist.txt
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-dev
#        key: config/datapoint_whitelist.txt
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-dev-dr
#        key: config/datapoint_whitelist.txt
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-dev-eu
#        key: config/datapoint_whitelist.txt
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/equity_datapoints.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-dev
#        key: config/equity_datapoints.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-dev-dr
#        key: config/equity_datapoints.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-dev-eu
#        key: config/equity_datapoints.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/code_mappings.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-dev
#        key: config/code_mappings.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-dev-dr
#        key: config/code_mappings.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-dev-eu
#        key: config/code_mappings.xml
#
#  - source:
#      accountId: "************"
#      region: us-east-1
#      bucket: velo-deploy-nonprod-us-east-1
#      key: mart-dps-config/{{sourceVersion}}/calculations.xml
#    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-dev
#        key: config/calculations.xml
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-dev-dr
#        key: config/calculations.xml
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-dev-eu
#        key: config/calculations.xml