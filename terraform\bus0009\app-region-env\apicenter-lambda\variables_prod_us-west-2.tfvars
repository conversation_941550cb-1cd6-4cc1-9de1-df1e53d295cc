region                = "us-west-2"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "prod"
environmentForTagging = "prd"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

account_env                           = "prod"
deploy_max                            = "20"
deploy_min                            = "5"
reserved_lambda_concurrent_executions = "50"
application_profile                   = "prod_dr"
dfd_lambda_access_group = [
  "arn:aws:iam::************:role/velo-stand-alone-prod",
  "arn:aws:iam::************:role/mart-role-prod"
]
log_retention_days = 3