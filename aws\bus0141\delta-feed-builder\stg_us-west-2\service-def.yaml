launchType: FARGATE
serviceName: stg-asyncdata-feed-worker
desiredCount: 0
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0cb0b481497065818 # private_app
    subnets:
      - subnet-03d560dd0e5253099 # us-west-2c-private
      - subnet-0cfbd3bd59d7c2cad # us-west-2b-private
      - subnet-084b8c666e71a339f # us-west-2a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 100
tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION