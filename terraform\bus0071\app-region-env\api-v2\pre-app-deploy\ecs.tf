module "eqapi-ongoing-dump-asg" {
  source           = "./../../../modules/ecs-asg-ec2"

  ec2_keypair      = var.ec2_keypair
  ecs_cluster_name = local.service_name
  environment      = var.environmentForTagging
  tsid             = var.tsid
  function         = var.function
  instance_size    = var.instance_size
  instance_type    = var.instance_type
  product          = "ongoing-dump"
  region           = var.region
  security_groups  = ["private_app","private_web","private_active_directory_client","console"]

  tags             = var.tags
  esg_tags         = var.esg_tags
  vpc_tag          = var.vpc_tag
  create_resource  = var.create_resource
  
}

module "eqapi-restful-api-ecs" {
  source                     = "./../../../modules/no-ecs-service-fargate"
  app_name                   = "${var.tsid}-${var.environmentForTagging}-${var.group}-restful-api"
  access_log_bucket          = var.access_log_bucket
  access_log_prefix          = "elb/${var.environmentForTagging}/restful-api"
  alb_health_check_interval  = var.alb_health_check_interval
  alb_health_check_path      = var.alb_health_check_path
  alb_health_check_timeout   = var.alb_health_check_timeout
  alb_healthy_threshold      = var.alb_healthy_threshold
  alb_unhealthy_threshold    = var.alb_unhealthy_threshold
  certificate_domain         = var.certificate_domain
  create_resource            = var.create_resource
  desired_count              = var.desired_count
  ecs_role_arn               = data.aws_iam_role.eqapi-restful-api-role.arn
  environment                = var.environmentForTagging
  group                      = var.group
  image                      = "${data.aws_ecr_repository.ecr_repository.repository_url}:${var.release_version}"
  log_group                  = "/aws/ecs/${var.environmentForTagging}-${var.group}-restful-api"
  java_opts                  = var.web_java_opts
  java_app_name              = "restful"
  command                    = ["--spring.profiles.active=${var.environmentForTagging}"]
  max_capacity               = var.max_capacity
  min_capacity               = var.min_capacity
  product                    = "restful-api"
  region                     = var.region
  tags                       = var.web_tags
  task_cpu                   = var.web_task_cpu
  task_mem                   = var.web_task_mem
  vpc_tag                    = var.vpc_tag
  subnets_ids                = var.subnets_ids
}
