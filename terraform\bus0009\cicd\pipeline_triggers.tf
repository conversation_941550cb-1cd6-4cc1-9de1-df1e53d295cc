module "apicenter" {
  source        = "./../../modules/pipeline_triggers"
  org_id        = var.org_id
  project_id    = var.project_id
  pipeline_id   = "apicenter"
  repo_name     = "dataac/apicenter"
  custom_input_sets = [
    {
      identifier  = "pr_to_input_set"
      name        = "PR-to-input-set"
      template  = {
        file_path = "${path.module}/templates/input_sets/default/pr.tftpl"
        variables = {
          skip_blackduck_scanning  = "yes"
          skip_checkmarx_scanning  = "no"
        }
      }
    },
    {
      identifier  = "push_to_input_set"
      name        = "Push-to-input-set"
      template = {
        file_path = "${path.module}/templates/input_sets/default/push.tftpl"
        variables = {
          skip_blackduck_scanning  = "no"
          skip_checkmarx_scanning  = "no"
        }
      }
    }
  ]
  triggers      = {
    pr_to_develop   = true
    pr_to_release   = true
    pr_to_master    = true
    push_to_master  = true
    push_to_develop = true
  }
}

module "apicenter-entitlement" {
  source        = "./../../modules/pipeline_triggers"
  org_id        = var.org_id
  project_id    = var.project_id
  pipeline_id   = "apicenterentitlement"
  repo_name     = "dataac/entitlement"
  custom_input_sets = [
    {
      identifier  = "pr_to_input_set"
      name        = "PR-to-input-set"
      template  = {
        file_path = "${path.module}/templates/input_sets/default/pr.tftpl"
        variables = {
          skip_blackduck_scanning  = "yes"
          skip_checkmarx_scanning  = "no"
        }
      }
    },
    {
      identifier  = "push_to_input_set"
      name        = "Push-to-input-set"
      template = {
        file_path = "${path.module}/templates/input_sets/default/push.tftpl"
        variables = {
          skip_blackduck_scanning  = "no"
          skip_checkmarx_scanning  = "no"
        }
      }
    }
  ]
  triggers      = {
    pr_to_develop   = true
    pr_to_release   = true
    pr_to_master    = true
    push_to_master  = true
    push_to_develop = true
  }
}

module "apicenter-lambda" {
  source        = "./../../modules/pipeline_triggers"
  org_id        = var.org_id
  project_id    = var.project_id
  pipeline_id   = "apicenterlambda"
  repo_name     = "dataac/apicenter-lambda"
  custom_input_sets = [
    {
      identifier  = "pr_to_input_set"
      name        = "PR-to-input-set"
      template  = {
        file_path = "${path.module}/templates/input_sets/default/pr.tftpl"
        variables = {
          skip_blackduck_scanning  = "yes"
          skip_checkmarx_scanning  = "no"
        }
      }
    },
    {
      identifier  = "push_to_input_set"
      name        = "Push-to-input-set"
      template = {
        file_path = "${path.module}/templates/input_sets/default/push.tftpl"
        variables = {
          skip_blackduck_scanning  = "no"
          skip_checkmarx_scanning  = "no"
        }
      }
    }
  ]
  triggers      = {
    pr_to_develop   = true
    pr_to_release   = true
    pr_to_master    = true
    push_to_master  = true
    push_to_develop = true
  }
}

module "fund-api" {
  source        = "./../../modules/pipeline_triggers"
  org_id        = var.org_id
  project_id    = var.project_id
  pipeline_id   = "fundapi"
  repo_name     = "dataac/apidata"
  custom_input_sets = [
    {
      identifier  = "pr_to_input_set"
      name        = "PR-to-input-set"
      template  = {
        file_path = "${path.module}/templates/input_sets/default/pr.tftpl"
        variables = {
          skip_blackduck_scanning  = "yes"
          skip_checkmarx_scanning  = "no"
        }
      }
    },
    {
      identifier  = "push_to_input_set"
      name        = "Push-to-input-set"
      template = {
        file_path = "${path.module}/templates/input_sets/default/push.tftpl"
        variables = {
          skip_blackduck_scanning  = "no"
          skip_checkmarx_scanning  = "no"
        }
      }
    }
  ]
  triggers      = {
    pr_to_develop   = true
    pr_to_release   = true
    pr_to_master    = true
    push_to_master  = true
    push_to_develop = true
  }
}

module "fund-api-v1" {
  source        = "./../../modules/pipeline_triggers"
  org_id        = var.org_id
  project_id    = var.project_id
  pipeline_id   = "fundapiv1"
  repo_name     = "dataac/apidata_v1"
  custom_input_sets = [
    {
      identifier  = "pr_to_input_set"
      name        = "PR-to-input-set"
      template  = {
        file_path = "${path.module}/templates/input_sets/default/pr.tftpl"
        variables = {
          skip_blackduck_scanning  = "yes"
          skip_checkmarx_scanning  = "no"
        }
      }
    },
    {
      identifier  = "push_to_input_set"
      name        = "Push-to-input-set"
      template = {
        file_path = "${path.module}/templates/input_sets/default/push.tftpl"
        variables = {
          skip_blackduck_scanning  = "no"
          skip_checkmarx_scanning  = "no"
        }
      }
    }
  ]
  triggers      = {
    pr_to_develop   = true
    pr_to_release   = true
    pr_to_master    = true
    push_to_master  = true
    push_to_develop = true
  }
}

module "apicenter-backend" {
  source        = "./../../modules/pipeline_triggers"
  org_id        = var.org_id
  project_id    = var.project_id
  pipeline_id   = "apicenterbackend"
  repo_name     = "dataac/apicenter_backend"
  custom_input_sets = [
    {
      identifier  = "pr_to_input_set"
      name        = "PR-to-input-set"
      template  = {
        file_path = "${path.module}/templates/input_sets/default/pr.tftpl"
        variables = {
          skip_blackduck_scanning  = "yes"
          skip_checkmarx_scanning  = "no"
        }
      }
    },
    {
      identifier  = "push_to_input_set"
      name        = "Push-to-input-set"
      template = {
        file_path = "${path.module}/templates/input_sets/default/push.tftpl"
        variables = {
          skip_blackduck_scanning  = "no"
          skip_checkmarx_scanning  = "no"
        }
      }
    }
  ]
  triggers      = {
    pr_to_develop   = true
    pr_to_release   = true
    pr_to_master    = true
    push_to_master  = true
    push_to_develop = true
  }
}
