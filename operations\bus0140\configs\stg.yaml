deploy_role_name: mstar-engr-cross-account-deploy

rds:
  - template:
      location: 'templates/rds.json.j2'
      variables:
        source_region: "us-east-1"
        instance_id: "essentials-stg"
        rds_instance_class: "db.t3.small"
        vpc_security_group_ids:
          - sg-08fba42adc9240966
          - sg-0a719818294fd3f0b
        tags:
          ENVIRONMENT: STG
          TID: DATAAC
          PID: PID0029
          SERVICEID: ts00669

ecs_services:
  - service_name: essentials-website-service-stg
    cluster_name: essentials-cluster-stg
    autoscaling: false
    min_capacity: 1
  - service_name: essentials-user-service-stg
    cluster_name: essentials-cluster-stg
    autoscaling: false
    min_capacity: 1
  - service_name: essentials-image-loader-service-stg
    cluster_name: essentials-cluster-stg
    autoscaling: false
    min_capacity: 1
  - service_name: essentials-imageapi-service-stg
    cluster_name: essentials-cluster-stg
    autoscaling: false
    min_capacity: 1

route53:
  zone_id: Z1763QDC3XWVS7
  location: 'templates/route53.json.j2'
  domains:
    - domain: essentials-stg.date7ebe.easn.morningstar.com
      records:
        - set_identifier: stg
          region: us-east-1
        - set_identifier: stg-dr
          region: us-west-2
    - domain: essentials-user-management-stg.date7ebe.easn.morningstar.com
      records:
        - set_identifier: stg
          region: us-east-1
        - set_identifier: stg-dr
          region: us-west-2
    - domain: essentials-image-loader-stg.date7ebe.easn.morningstar.com
      records:
        - set_identifier: stg
          region: us-east-1
        - set_identifier: stg-dr
          region: us-west-2
    - domain: essentials-image-api-stg.date7ebe.easn.morningstar.com
      records:
        - set_identifier: stg
          region: us-east-1
        - set_identifier: stg-dr
          region: us-west-2