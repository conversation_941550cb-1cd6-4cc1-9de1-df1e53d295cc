name: Promote Artifacts from dev to prod bucket
items:
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: mart-aws-message-worker/{{sourceVersion}}/delta-msg-worker-function.zip
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: mart-aws-message-worker/{{targetVersion}}/delta-msg-worker-function.zip
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: mart-aws-message-worker/{{sourceVersion}}/delta-msg-relay-function.zip
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: mart-aws-message-worker/{{targetVersion}}/delta-msg-relay-function.zip
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: mart-aws-message-worker/{{sourceVersion}}/delta-lake-relay-function.zip
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-prod-us-east-1
        key: mart-aws-message-worker/{{targetVersion}}/delta-lake-relay-function.zip