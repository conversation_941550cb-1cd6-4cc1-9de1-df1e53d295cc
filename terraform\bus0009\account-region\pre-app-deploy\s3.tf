resource "aws_s3_bucket" "apicenter_bucket" {
  bucket        = var.bucket_name
  force_destroy = var.force_destroy
  tags          = local.tags
}

resource "aws_s3_bucket_acl" "apicenter_bucket_acl" {
  bucket = aws_s3_bucket.apicenter_bucket.id
  acl    = "private"

  depends_on = [aws_s3_bucket_ownership_controls.apicenter_bucket_acl_ownership]
}

resource "aws_s3_bucket_ownership_controls" "apicenter_bucket_acl_ownership" {
  bucket = aws_s3_bucket.apicenter_bucket.id
  rule {
    object_ownership = "ObjectWriter"
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "apicenter_bucket_lifecycle_configuration" {
  bucket = aws_s3_bucket.apicenter_bucket.id

  rule {
    id = "expiration"

    expiration {
      days = 14
    }

    status = "Enabled"
  }
}

resource "aws_s3_bucket_policy" "apicenter_bucketpolicy" {
  bucket = aws_s3_bucket.apicenter_bucket.id
  policy = data.aws_iam_policy_document.apicenter_bucket_policy.json
}

resource "aws_s3_bucket" "apicenter_data_bucket" {
  bucket = var.data_bucket_name

  lifecycle {
    prevent_destroy = false
  }

  tags = local.tags
}

resource "aws_s3_bucket_lifecycle_configuration" "apicenter_data_s3_rule" {
  bucket = aws_s3_bucket.apicenter_data_bucket.id

  rule {
    id     = "delete-old-lucene-files"
    status = "Enabled"

    filter {
      prefix = "Lucene/apidata"
    }
    abort_incomplete_multipart_upload {
      days_after_initiation = 1
    }

    expiration {
      days = 7
    }
  }
}

resource "aws_s3_bucket_versioning" "apicenter_data_s3_versioning" {
  bucket = aws_s3_bucket.apicenter_data_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_ownership_controls" "apicenter_data_s3_ownership_controls" {
  bucket = aws_s3_bucket.apicenter_data_bucket.id
  rule {
    object_ownership = "BucketOwnerEnforced"
  }
}

resource "aws_s3_bucket_policy" "mart-data-s3-policy" {
  bucket = aws_s3_bucket.apicenter_data_bucket.id
  policy = <<POLICY
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "us-east-1.elasticache-snapshot.amazonaws.com"
            },
            "Action": [
                "s3:PutObject",
                "s3:GetObject",
                "s3:ListBucket",
                "s3:GetBucketAcl",
                "s3:ListMultipartUploadParts",
                "s3:ListBucketMultipartUploads"
            ],
            "Resource": [
                "${aws_s3_bucket.apicenter_data_bucket.arn}/*",
                "${aws_s3_bucket.apicenter_data_bucket.arn}"
            ]
        }
    ]
}
  POLICY
}
