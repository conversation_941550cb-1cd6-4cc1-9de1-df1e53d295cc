name: Copy Artifacts to S3
items:
  - sourceFile: Velo-front-end/velo-api/velo-lambda/app-async-universe-list/target/app-async-universe-list.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: velo-lambda/app-async-universe-list/{{version}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-nonprod-us-west-2
        key: velo-lambda/app-async-universe-list/{{version}}/app.jar
  - sourceFile: Velo-front-end/velo-api/velo-lambda/app-feed-dfd/target/app-feed-dfd.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: velo-lambda/app-feed-dfd/{{version}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-nonprod-us-west-2
        key: velo-lambda/app-feed-dfd/{{version}}/app.jar
  - sourceFile: Velo-front-end/velo-api/velo-lambda/app-feed-fdo/target/app-feed-fdo.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: velo-lambda/app-feed-fdo/{{version}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-nonprod-us-west-2
        key: velo-lambda/app-feed-fdo/{{version}}/app.jar
  - sourceFile: Velo-front-end/velo-api/velo-lambda/app-feed-message/target/app-feed-message.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: velo-lambda/app-feed-message/{{version}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-nonprod-us-west-2
        key: velo-lambda/app-feed-message/{{version}}/app.jar
  - sourceFile: Velo-front-end/velo-api/velo-lambda/app-feed-notification-handler/target/app-feed-notification-handler.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: velo-lambda/app-feed-notification-handler/{{version}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-nonprod-us-west-2
        key: velo-lambda/app-feed-notification-handler/{{version}}/app.jar        
  - sourceFile: Velo-front-end/velo-api/velo-lambda/app-notebook-sns-listener/target/app-notebook-sns-listener.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: velo-lambda/app-notebook-sns-listener/{{version}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-nonprod-us-west-2
        key: velo-lambda/app-notebook-sns-listener/{{version}}/app.jar
  - sourceFile: Velo-front-end/velo-api/velo-lambda/disable-feed-expired-licenses/target/disable-feed-expired-licenses.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: velo-lambda/disable-feed-expired-licenses/{{version}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-nonprod-us-west-2
        key: velo-lambda/disable-feed-expired-licenses/{{version}}/app.jar
  - sourceFile: Velo-front-end/velo-api/velo-lambda/standard-feed-sns-notification/target/standard-feed-sns-notification.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: velo-lambda/standard-feed-sns-notification/{{version}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-nonprod-us-west-2
        key: velo-lambda/standard-feed-sns-notification/{{version}}/app.jar
  - sourceFile: Velo-front-end/velo-api/velo-lambda/sync-index-series-to-entitlement/target/sync-index-series-to-entitlement.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: velo-lambda/sync-index-series-to-entitlement/{{version}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-nonprod-us-west-2
        key: velo-lambda/sync-index-series-to-entitlement/{{version}}/app.jar
  - sourceFile: Velo-front-end/velo-api/velo-lambda/app-equity-delta-sns-listener/target/equity-delta-sns-listener.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: velo-deploy-nonprod-us-east-1
        key: velo-lambda/equity-delta-sns-listener/{{version}}/app.jar
      - accountId: "************"
        region: us-west-2
        bucket: velo-deploy-nonprod-us-west-2
        key: velo-lambda/equity-delta-sns-listener/{{version}}/app.jar