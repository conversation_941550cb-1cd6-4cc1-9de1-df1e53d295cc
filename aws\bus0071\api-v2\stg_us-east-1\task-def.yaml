family: ts00804-stg-eqapi-restful-api-ecs-task
executionRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/ts00804-stg-us-east-1-app-restful-api-role
taskRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/ts00804-stg-us-east-1-app-restful-api-role
containerDefinitions:
  - name: stg-restful-api-container
    image: <+variable.AWS_NONPROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/eqapi/api-v2:<+serviceVariables.ImageTag>
    cpu: 0
    portMappings:
      - hostPort: 80
        protocol: tcp
        containerPort: 80
    essential: true
    environment:
      - name: JAVA_OPTS
        value: -Dnewrelic.environment=staging -javaagent:/usr/local/newrelic/newrelic.jar -XX:MaxRAMPercentage=80.0 -XX:MinRAMPercentage=40.0 -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC
      - name: APP_NAME
        value: restful
      - name: SPRING_PROFILES_ACTIVE
        value: stg
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /aws/ecs/stg-eqapi-restful-api
        awslogs-region: us-east-1
        awslogs-stream-prefix: stg

cpu: '2048'
memory: '4096'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>