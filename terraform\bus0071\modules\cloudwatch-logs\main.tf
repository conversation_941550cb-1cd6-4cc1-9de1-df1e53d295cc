data "aws_kinesis_stream" "stream" {
  name = "mstar-kinesis-datasvc-${lookup(local.kinesis_map, var.environment, "prod")}-splunk-${var.region}"
}

data "aws_iam_role" "log_role" {
  name = "CWLtoKinesisRole"
}

resource "aws_cloudwatch_log_group" "cloudwatch_log_group" {
  name = var.log_group
  retention_in_days = 30
  tags = merge(var.tags)
}

resource "aws_cloudwatch_log_subscription_filter" "filter" {
  name            = "${var.log_group}-log-subscription-${var.environment}"
  log_group_name  = aws_cloudwatch_log_group.cloudwatch_log_group.name
  filter_pattern  = ""
  destination_arn = data.aws_kinesis_stream.stream.arn
  role_arn        = data.aws_iam_role.log_role.arn
}