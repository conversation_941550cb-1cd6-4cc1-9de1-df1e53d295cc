deploy_role_name: mstar-engr-cross-account-deploy

rds:
  - template:
      location: 'templates/rds.json.j2'
      variables:
        source_region: "us-east-1"
        instance_id: "essentials-dev"
        rds_instance_class: "db.t3.small"
        vpc_security_group_ids:
          - sg-08fba42adc9240966
          - sg-0a719818294fd3f0b
        tags:
          ENVIRONMENT: DEV
          TID: DATAAC
          PID: PID0029
          SERVICEID: ts00669

ecs_services:
  - service_name: essentials-website-service-dev
    cluster_name: essentials-cluster-dev
    autoscaling: false
    min_capacity: 1
  - service_name: essentials-user-service-dev
    cluster_name: essentials-cluster-dev
    autoscaling: false
    min_capacity: 1
  - service_name: essentials-image-loader-service-dev
    cluster_name: essentials-cluster-dev
    autoscaling: false
    min_capacity: 1
  - service_name: essentials-imageapi-service-dev
    cluster_name: essentials-cluster-dev
    autoscaling: false
    min_capacity: 1