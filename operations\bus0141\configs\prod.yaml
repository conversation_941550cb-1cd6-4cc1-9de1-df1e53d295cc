deploy_role_name: mstar-engr-cross-account-deploy

# ECS Services
ecs_services:
  - service_name: velo-standalone-service-prod  # Website
    cluster_name: velo-stand-alone-cluster-prod
    autoscaling: true
    min_capacity: 1
  - service_name: velo-schedule-service-prod  # Scheduler
    cluster_name: velo-stand-alone-cluster-prod
    autoscaling: true
    min_capacity: 1
  - service_name: velo-feed-api-service-prod  # External API
    cluster_name: velo-stand-alone-cluster-prod
    autoscaling: true
    min_capacity: 1
  - service_name: mart-monitor-service-prod  # Mart Monitor
    cluster_name: velo-stand-alone-cluster-prod
    autoscaling: false
    min_capacity: 1

# SSM Parameters
ssm:
  source_region: us-east-1
  sync_tag_key: SyncAcrossRegions

# Schedulers Configuration
scheduler:
  - template:
      location: 'templates/sqs_schedule_switch.json.j2'
      variables:
        message_target_group: velo
        url: https://sqs.us-east-1.amazonaws.com/************/velo-scheduler-sqs-prod.fifo
  - template:
      location: 'templates/sqs_schedule_switch.json.j2'
      variables:
        message_target_group: fdo
        url: https://sqs.us-east-1.amazonaws.com/************/velo-scheduler-sqs-prod.fifo

# Elasticsearch Configuration
es:
  - domain_name: change-notification
    template:
      location: 'templates/es-change-notification.json.j2'
      variables:
        domain_name: change-notification
        access_policy: '{\"Version\": \"2012-10-17\",\"Statement\": [{\"Effect\": \"Allow\",\"Principal\": {\"AWS\": \"*\"},\"Action\": \"es:*\",\"Resource\": \"arn:aws:es:us-west-2:************:domain/change-notification/*\"}]}'
        subnets:
          - subnet-001ccb29fa360fac8
          - subnet-04b0eee56af730a20
          - subnet-00a0c2588e6af526c
        security_group_ids:
          - sg-01116f50a47ead028
        tags:
          TID: DATAAC
          PID: PID0574
          SERVICEID: ts01004
    route53:
      template:
        location: 'templates/route53/change-notification.json.j2'
        variables:
          zone_id: ZPHK6BJ6MT62F
          domain_name: datasvc-change-notification-dr.dat688b3.eas.morningstar.com
    snapshot:
      lambda: backup-snapshot-prod
      restore:
        payload:
          task: changeNotificationES
          action: restore_change_notification
      create:
        payload:
          task: changeNotificationES
  - domain_name: s-mart-prod-search
    snapshot:
      lambda: backup-snapshot-prod
      restore:
        payload:
          task: martFeedES
          action: restore
          es_name: changelog
          es_restore_snapshot_name: latest
  - domain_name: mart-changelog-prod
    snapshot:
      lambda: backup-snapshot-prod
      restore:
        payload:
          task: martFeedES
          action: restore
          es_name: martES
          es_restore_snapshot_name: latest

# Route53 Configuration
route53:
  zone_id: ZPHK6BJ6MT62F
  records:
    - name: Website
      lb_name: velo-standalone-lb-prod
      template:
        location: 'templates/route53/website.json.j2'
        variables:
          identifier: prod
          domain_name: velo-standalone.dat688b3.eas.morningstar.com
    - name: External
      lb_name: velo-feed-api-lb-prod
      template:
        location: 'templates/route53/external.json.j2'
        variables:
          domain_name: velo-feed-api-prod.dat688b3.eas.morningstar.com
    - name: Monitor
      lb_name: mart-monitor-lb-prod
      template:
        location: 'templates/route53/monitor.json.j2'
        variables:
          identifier: prod-us-east-1
          domain_name: mart-monitor-prod.dat688b3.eas.morningstar.com

lambda:
  - name: standalone-velo-lambda-dfd-generator-prod
    environment:
      variables:
        - name: datasource_session_variable
          dr_testing_value: "&sessionVariables=aurora_replica_read_consistency=global"
          value: ""
  - name: standalone-velo-lambda-async-universe-list-prod
    environment:
      variables:
        - name: datasource_session_variable
          dr_testing_value: "&sessionVariables=aurora_replica_read_consistency=global"
          value: ""
  - name: standalone-velo-lambda-fdo-prod
    environment:
      variables:
        - name: datasource_session_variable
          dr_testing_value: "&sessionVariables=aurora_replica_read_consistency=global"
          value: ""
  - name: standalone-velo-lambda-feed-message-consumer-prod
    environment:
      variables:
        - name: datasource_session_variable
          dr_testing_value: "&sessionVariables=aurora_replica_read_consistency=global"
          value: ""
  - name: standalone-velo-lambda-notification-handler-prod
    environment:
      variables:
        - name: datasource_session_variable
          dr_testing_value: "&sessionVariables=aurora_replica_read_consistency=global"
          value: ""
  - name: velo-entitlement-sync-index-series-prod
    environment:
      variables:
        - name: datasource_session_variable
          dr_testing_value: "&sessionVariables=aurora_replica_read_consistency=global"
          value: ""
  - name: velo-disable-feed-expired-licenses-prod
    environment:
      variables:
        - name: datasource_session_variable
          dr_testing_value: "&sessionVariables=aurora_replica_read_consistency=global"
          value: ""