launchType: FARGATE
serviceName: mart-monitor-service-prod
desiredCount: 0
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-01116f50a47ead028 # private_web
      - sg-0c62d1668d1a136cd # private_app
      - sg-04369231c2b3296ce # private_db
      - sg-0ab635deffd51b1b2 # console
      - sg-0990b9c75cae7eebd # private_internal_email_morningstar
    subnets:
      - subnet-001ccb29fa360fac8 # us-west-2c-private
      - subnet-04b0eee56af730a20 # us-west-2a-private
      - subnet-00a0c2588e6af526c # us-west-2b-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 50

healthCheckGracePeriodSeconds: 300

loadBalancers:
 - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
   containerName: mart-monitor-container-prod
   containerPort: 8080
tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION