region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "uat"
environmentForTagging = "uat"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

account_env                           = "prod"
deploy_max                            = "10"
deploy_min                            = "0"
reserved_lambda_concurrent_executions = "30"
application_profile                   = "uat"
dfd_lambda_access_group = [
  "arn:aws:iam::************:role/velo-stand-alone-prod",
  "arn:aws:iam::************:role/mart-role-uat"
]
log_retention_days = 3