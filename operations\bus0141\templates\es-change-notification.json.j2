{
  "DomainName": "{{ domain_name }}",
  "ElasticsearchVersion": "7.1",
  "ElasticsearchClusterConfig": {
    "InstanceType": "m5.xlarge.elasticsearch",
    "InstanceCount": 3,
    "DedicatedMasterEnabled": false,
    "ZoneAwarenessEnabled": true,
    "ZoneAwarenessConfig": {
      "AvailabilityZoneCount": 3
    },
    "WarmEnabled": false
  },
  "EBSOptions": {
    "EBSEnabled": true,
    "VolumeType": "gp2",
    "VolumeSize": 60
  },
  "AccessPolicies": "{{ access_policy }}",
  "VPCOptions": {
    "SubnetIds": [
      {% for subnet in subnets %}
      "{{ subnet }}"
      {% if not loop.last %},{% endif %}
      {% endfor %}
    ],
    "SecurityGroupIds": [
      {% for securityGroupId in security_group_ids %}
      "{{ securityGroupId }}"
      {% if not loop.last %},{% endif %}
      {% endfor %}
    ]
  },
  "CognitoOptions": {
    "Enabled": false
  },
  "EncryptionAtRestOptions": {
    "Enabled": false
  },
  "NodeToNodeEncryptionOptions": {
    "Enabled": false
  },
  "DomainEndpointOptions": {
    "EnforceHTTPS": false,
    "CustomEndpointEnabled": false
  },
  "AdvancedSecurityOptions": {
    "Enabled": false,
    "InternalUserDatabaseEnabled": false,
    "AnonymousAuthEnabled": false
  },
  "AutoTuneOptions": {
    "DesiredState": "DISABLED"
  },
  "TagList": [
    {% for key, value in tags.items() %}
    {
      "Key": "{{ key }}",
      "Value": "{{ value }}"
    }
    {% if not loop.last %},{% endif %}
    {% endfor %}
  ]
}