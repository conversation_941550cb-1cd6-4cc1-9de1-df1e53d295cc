launchType: FARGATE
serviceName: asyncdata-feed-worker
desiredCount: 2
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0d788a43acb654693 # private_app
    subnets:
      - subnet-007aa27f3f50932a2 # us-east-1c-private
      - subnet-08f032a54afd01bcc # us-east-1a-private
      - subnet-0adafb2337a7adb7a # us-east-1b-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 100
tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION