name: Copy Artifacts from versioned to prod/green bucket
items:
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-prod-us-east-1
      key: mart-dps-config/{{sourceVersion}}/dps-config.zip
      expandZip: true
    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-prod
#        key: config/
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: config/
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-prod-eu
#        key: config/
      # Blue Environment for B/G Deployment
      - accountId: "************"
        region: us-east-1
        bucket: mart-data-prod
        key: green-env-config/
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-prod-dr
#        key: green-env-config/
      - accountId: "************"
        region: eu-west-1
        bucket: mart-data-prod-eu
        key: green-env-config/
