launchType: FARGATE
serviceName: ts00804-prd-eqapi-restful-api
desiredCount: 4
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-d83f4ea4 # private_app
      - sg-d93f4ea5 # private_web
    subnets:
      - subnet-88396ba5 
      - subnet-040f0c4d
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 400
  minimumHealthyPercent: 50

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: prd-restful-api-container
    containerPort: 80