family: apicenter-entitlement-task-uat
executionRoleArn: arn:aws:iam::<+variable.AWS_PROD_ACCOUNT_ID>:role/mart-role-uat
taskRoleArn: arn:aws:iam::<+variable.AWS_PROD_ACCOUNT_ID>:role/mart-role-uat
containerDefinitions:
  - name: apicenter-entitlement-container-uat
    image: <+variable.AWS_PROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/apicenter-entitlement-ecr:<+serviceVariables.ImageTag>
    cpu: 512
    memory: 1024
    portMappings:
      - hostPort: 8080
        protocol: tcp
        containerPort: 8080
    essential: true
    environment:
      - name: ACTIVE_ENV
        value: "uat"
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /aws/ecs/apicenter-entitlement-uat
        awslogs-region: us-east-1
        awslogs-stream-prefix: ecs
    secrets:
      - name: SENDGRID_APIKEY
        valueFrom: "arn:aws:ssm:us-east-1:************:parameter/FUNDDATAP/SEND_GRID_UAT"
cpu: '512'
memory: '1024'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: NAME
    value: apicenter-entitlement-uat
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
  - key: PID
    value: PID0029
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
