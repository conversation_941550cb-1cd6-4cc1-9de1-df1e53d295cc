region                = "us-west-2"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "dev"
environmentForTagging = "dev"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

min_count                    = 0
max_count                    = 1
peak_min_count               = 0
peak_max_count               = 1
ecs_task_schedule_up         = "cron(0 5 ? * MON,TUE,WED,THU,FRI *)"
ecs_task_schedule_down       = "cron(30 5 ? * MON,TUE,WED,THU,FRI *)"
cloudwatch_evaluation_period = 1
cloudwatch_period            = 180
enable_vo_alert              = false