name: Copy Artifacts from versioned to stg/green bucket
items:
  - source:
      accountId: "************"
      region: us-east-1
      bucket: velo-deploy-nonprod-us-east-1
      key: mart-dps-config/{{sourceVersion}}/dps-config.zip
      expandZip: true
    targets:
#      - accountId: "************"
#        region: us-east-1
#        bucket: mart-data-stg
#        key: config/
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-stg-dr
#        key: config/
#      - accountId: "************"
#        region: eu-west-1
#        bucket: mart-data-stg-eu
#        key: config/
      # Blue Environment for B/G Deployment
      - accountId: "************"
        region: us-east-1
        bucket: mart-data-stg
        key: green-env-config/
#      - accountId: "************"
#        region: us-west-2
#        bucket: mart-data-stg-dr
#        key: green-env-config/
      - accountId: "************"
        region: eu-west-1
        bucket: mart-data-stg-eu
        key: green-env-config/
