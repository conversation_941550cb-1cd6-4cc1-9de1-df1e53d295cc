family: velo-feed-api-task-definition-stg
executionRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/velo-stand-alone-stg
taskRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/velo-stand-alone-stg
containerDefinitions:
  - name: velo-feed-api-container-stg
    image: <+variable.AWS_NONPROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/velo-feed-api-ecr:<+serviceVariables.ImageTag>
    cpu: 1024
    memory: 2048
    portMappings:
      - hostPort: 8080
        protocol: tcp
        containerPort: 8080
    essential: true
    environment:
      - name: ACTIVE_ENV
        value: stg-us-west-2
      - name: EXTERNAL_API_DATA_UPDATES_ENABLED
        value: 1
      - name: aws_region
        value: us-west-2
      - name: datasource_session_variable
        value: <+serviceVariables.DATASOURCE_SESSION_VARIABLE>
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /ecs/velo-feed-api-stg
        awslogs-region: us-west-2
        awslogs-stream-prefix: ecs
    secrets:
      - name: SPRING_DATASOURCE_WRITE_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/VELO_WEBSITE/DB_PASSWORD"
      - name: SPRING_DATASOURCE_READ_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/VELO_WEBSITE/DB_PASSWORD"
      - name: APIGEE_CLIENTID
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/VELO/APIGEE_CLIENT_ID"
      - name: APIGEE_CLIENTSECRET
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/VELO/APIGEE_CLIENT_SECRET"
      - name: VELOADMIN_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/VELO_WEBSITE/ADMIN_PASSWORD"
      - name: NOTEBOOK_ENCRYPTION_SECRETKEY
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/encryption_secretkey"
      - name: AWS_IAM_STDFEED_ACCESSKEY
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/VELO/STD_FEED_IAM_ACCESS_KEY"
      - name: AWS_IAM_STDFEED_SECRETKEY
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_NONPROD_ACCOUNT_ID>:parameter/VELO/STD_FEED_IAM_SECRET_KEY"

cpu: '1024'
memory: '2048'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>