region                = "us-west-2"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "prod"
environmentForTagging = "prd"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

min_count                    = 0
max_count                    = 12
peak_min_count               = 0
peak_max_count               = 12
ecs_task_schedule_up         = "cron(50 2,13 ? * MON,TUE,WED,THU,FRI *)"
ecs_task_schedule_down       = "cron(5 8,18 ? * MON,TUE,WED,THU,FRI *)"
cloudwatch_evaluation_period = 1
cloudwatch_period            = 180
route53_name                 = "dat688b3.eas.morningstar.com"
route53_weight               = 0
is_dr                        = true
enable_vo_alert              = true