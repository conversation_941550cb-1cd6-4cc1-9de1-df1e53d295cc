module "auto_scaling_policy" {
  source                = "./../../../modules/autoscaling-ecs"
  alb_name              = "${var.tsid}-${var.environmentForTagging}-eqapi-restful-api"
  app_name              = "${var.tsid}-${var.environmentForTagging}-eqapi-restful-api"
  create_resource       = var.create_resource
  max_capacity          = var.max_capacity
  min_capacity          = var.min_capacity
  tags                  = var.tags

}