launchType: FARGATE
serviceName: essentials-imageapi-service-prod
desiredCount: 1
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0d788a43acb654693 # private_app
      - sg-0e7ef1010d6acddf8 # private_web
      - sg-078ee4651c9f093eb # private_db
    subnets:
      - subnet-007aa27f3f50932a2
      - subnet-08f032a54afd01bcc
      - subnet-0adafb2337a7adb7a
    assignPublicIp: DISABLED
deploymentConfiguration:
  maximumPercent: 200
  minimumHealthyPercent: 50
healthCheckGracePeriodSeconds : 300
schedulingStrategy : REPLICA
loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: essentials-imageapi-container-prod
    containerPort: 8080
tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION
