family: apicenter-entitlement-task-prod
executionRoleArn: arn:aws:iam::<+variable.AWS_PROD_ACCOUNT_ID>:role/mart-role-prod
taskRoleArn: arn:aws:iam::<+variable.AWS_PROD_ACCOUNT_ID>:role/mart-role-prod
containerDefinitions:
  - name: apicenter-entitlement-container-prod
    image: <+variable.AWS_PROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/apicenter-entitlement-ecr:<+serviceVariables.ImageTag>
    cpu: 512
    memory: 1024
    portMappings:
      - hostPort: 8080
        protocol: tcp
        containerPort: 8080
    essential: true
    environment:
      - name: ACTIVE_ENV
        value: "prod"
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /aws/ecs/apicenter-entitlement-prod
        awslogs-region: us-east-1
        awslogs-stream-prefix: ecs
    secrets:
      - name: SENDGRID_APIKEY
        valueFrom: "arn:aws:ssm:us-east-1:************:parameter/FUNDDATAP/SEND_GRID_PROD"
cpu: '512'
memory: '1024'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: NAME
    value: apicenter-entitlement-prod
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
  - key: PID
    value: PID0029
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
