# Application Load Balancer
resource "aws_lb" "apicenter_lb" {
  load_balancer_type = "application"
  idle_timeout       = each.value["idle_timeout"]
  for_each           = var.load_balancers
  name               = "${each.value["application_name"]}-alb-${local.env_map[var.environment]}"
  internal           = each.value["is_internal"]
  subnets            = each.value["is_internal"] ? [
    module.region_data.private_subnet_1_id[0],
    module.region_data.private_subnet_2_id[0],
    module.region_data.private_subnet_3_id[0]
  ] : [
    module.region_data.public_subnet_1_id[0],
    module.region_data.public_subnet_2_id[0],
    module.region_data.public_subnet_3_id[0]
  ]
  security_groups = concat([
    module.region_data.security_group_private_app[0],
    module.region_data.security_group_private_db[0],
    module.region_data.security_group_private_web[0]
  ], each.value["extra_security_groups"] != null ?
    data.aws_security_groups.apicenter_extra_security_groups[each.key].ids : [])

  access_logs {
    enabled = each.value["enable_access_logs"]
    bucket  = var.bucket_name
    prefix  = each.value["application_name"]
  }

  tags = merge(local.tags, {
    SERVICEID = "ts00057"
    PID       = "PID0029"
    FUNCTION  = "app"
  })
}

resource "aws_lb_listener" "apicenter_lb_http_listener" {
  for_each          = aws_lb.apicenter_lb
  load_balancer_arn = each.value["arn"]
  port              = 80
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }

  depends_on = [aws_lb.apicenter_lb]
}

resource "aws_lb_listener" "apicenter_lb_https_listener" {
  for_each          = aws_lb.apicenter_lb
  load_balancer_arn = each.value["arn"]
  port              = 443
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS-1-2-2017-01"
  certificate_arn   = data.aws_acm_certificate.route53_certificate.arn

  default_action {
    type = "fixed-response"

    fixed_response {
      content_type = "text/plain"
      message_body = "Page Not Found"
      status_code  = "404"
    }
  }

  depends_on = [aws_lb.apicenter_lb]
}

resource "aws_lb_listener" "apicenter_lb_test_listener" {
  for_each = {
    for k, v in aws_lb.apicenter_lb : k => v
    if can(regex(".*apicenter-alb.*", v.name))
  }
  load_balancer_arn = each.value["arn"]
  port              = 8080
  protocol          = "HTTP"

  default_action {
    type = "fixed-response"

    fixed_response {
      content_type = "text/plain"
      message_body = "Page Not Found"
      status_code  = "404"
    }
  }

  depends_on = [aws_lb.apicenter_lb]
}

# Disable HTTP OPTIONS due to security
resource "aws_alb_listener_rule" "host_based_routing" {
  for_each = {
    for k, v in aws_lb_listener.apicenter_lb_https_listener : k => v
    if can(regex(".*apicenter-alb.*", v.arn))
  }
  listener_arn = each.value["arn"]
  priority     = 10

  action {
    type = "fixed-response"

    fixed_response {
      content_type = "text/plain"
      message_body = "403 Forbidden"
      status_code  = "403"
    }
  }

  condition {
    http_request_method {
      values = ["OPTIONS"]
    }
  }

  depends_on = [aws_lb.apicenter_lb]

}