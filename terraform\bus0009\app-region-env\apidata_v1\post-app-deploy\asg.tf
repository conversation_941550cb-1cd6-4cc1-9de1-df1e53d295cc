resource "aws_appautoscaling_target" "fund_api_auto_scaling_target" {
  max_capacity       = var.max_count
  min_capacity       = var.min_count
  resource_id        = "service/${data.aws_ecs_cluster.fund_api_cluster.cluster_name}/${data.aws_ecs_service.fund_api_service.service_name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"
  role_arn           = data.aws_iam_role.mart-role.arn

  lifecycle {
    ignore_changes = [role_arn]
  }

  depends_on = [data.aws_ecs_service.fund_api_service]
}

resource "aws_appautoscaling_policy" "fund_api_service_scale_up_policy" {
  name               = "${var.application_name}-service-scale-up-policy-${var.environment}"
  policy_type        = "StepScaling"
  resource_id        = aws_appautoscaling_target.fund_api_auto_scaling_target.resource_id
  scalable_dimension = aws_appautoscaling_target.fund_api_auto_scaling_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.fund_api_auto_scaling_target.service_namespace

  step_scaling_policy_configuration {
    adjustment_type         = "ChangeInCapacity"
    cooldown                = 120
    metric_aggregation_type = "Average"

    step_adjustment {
      metric_interval_lower_bound = 0
      scaling_adjustment          = 1
    }
  }
}

resource "aws_appautoscaling_policy" "fund_api_service_scale_down_policy" {
  name               = "${var.application_name}-service-scale-down-policy-${var.environment}"
  policy_type        = "StepScaling"
  resource_id        = aws_appautoscaling_target.fund_api_auto_scaling_target.resource_id
  scalable_dimension = aws_appautoscaling_target.fund_api_auto_scaling_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.fund_api_auto_scaling_target.service_namespace

  step_scaling_policy_configuration {
    adjustment_type         = "ChangeInCapacity"
    cooldown                = 300
    metric_aggregation_type = "Average"

    step_adjustment {
      metric_interval_upper_bound = 0
      scaling_adjustment          = -1
    }
  }
}

resource "aws_appautoscaling_scheduled_action" "fund_api_service_peak_schedule_up" {
  name               = "${var.application_name}-peak-schedule-up-${var.environment}"
  service_namespace  = aws_appautoscaling_target.fund_api_auto_scaling_target.service_namespace
  resource_id        = aws_appautoscaling_target.fund_api_auto_scaling_target.resource_id
  scalable_dimension = aws_appautoscaling_target.fund_api_auto_scaling_target.scalable_dimension
  schedule           = var.ecs_task_schedule_up

  scalable_target_action {
    min_capacity = var.peak_min_count
    max_capacity = var.peak_max_count
  }

  depends_on = [aws_appautoscaling_target.fund_api_auto_scaling_target]
}


resource "aws_appautoscaling_scheduled_action" "fund_api_service_peak_schedule_down" {
  name               = "${var.application_name}-peak-schedule-down-${var.environment}"
  service_namespace  = aws_appautoscaling_target.fund_api_auto_scaling_target.service_namespace
  resource_id        = aws_appautoscaling_target.fund_api_auto_scaling_target.resource_id
  scalable_dimension = aws_appautoscaling_target.fund_api_auto_scaling_target.scalable_dimension
  schedule           = var.ecs_task_schedule_down

  scalable_target_action {
    min_capacity = var.min_count
    max_capacity = var.max_count
  }

  depends_on = [aws_appautoscaling_target.fund_api_auto_scaling_target]
}



