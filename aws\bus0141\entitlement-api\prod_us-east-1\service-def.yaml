launchType: FARGATE
serviceName: prod-entitlement-api-service-nlb
desiredCount: 2
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0e7ef1010d6acddf8 # private_app
      - sg-0d788a43acb654693 # private_web
      - sg-078ee4651c9f093eb # private_db
    subnets:
      - subnet-007aa27f3f50932a2 # us-east-1c-private
      - subnet-08f032a54afd01bcc # us-east-1b-private
      - subnet-0adafb2337a7adb7a # us-east-1a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  minimumHealthyPercent: 50

healthCheckGracePeriodSeconds: 300

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: prod-entitlement-api
    containerPort: 8443

tags:
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: ORG
    value: <+serviceVariables.ORG>
  - key: TID
    value: <+variable.TID>
  - key: PID
    value: <+serviceVariables.PID>
  - key: MANAGED
    value: <+serviceVariables.MANAGED>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>

propagateTags: TASK_DEFINITION