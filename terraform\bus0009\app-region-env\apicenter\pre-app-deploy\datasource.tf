data "aws_kinesis_stream" "stream" {
  name = "mstar-kinesis-dataac-${local.kinesis_map[var.environment]}-splunk-${var.region}"
}

data "aws_iam_role" "log_role" {
  name = "CWLtoKinesis"
}

data "aws_vpc" "main" {
  filter {
    name   = "tag:Name"
    values = [var.vpc]
  }
}

data "aws_lb" "apicenter_lb" {
  name = local.alb_map[var.environment]
}

data "aws_lb_listener" "apicenter_lb_listener" {
  load_balancer_arn = data.aws_lb.apicenter_lb.arn
  port              = 443
}

data "aws_acm_certificate" "ms_certificate" {
  domain   = "api${local.env_map[var.environment]}.morningstar.com"
  statuses = ["ISSUED"]
}