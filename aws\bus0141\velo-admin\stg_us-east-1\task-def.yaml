family: velo-admin-definition-stg
executionRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/velo-stand-alone-stg
taskRoleArn: arn:aws:iam::<+variable.AWS_NONPROD_ACCOUNT_ID>:role/velo-stand-alone-stg
containerDefinitions:
  - name: velo-admin-container-stg
    image: <+variable.AWS_NONPROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/velo-admin-ecr:<+serviceVariables.ImageTag>
    cpu: 1024
    memory: 2048
    portMappings:
      - hostPort: 8080
        protocol: tcp
        containerPort: 8080
    essential: true
    environment:
      - name: ACTIVE_ENV
        value: stg
      - name: datasource_session_variable
        value: <+serviceVariables.DATASOURCE_SESSION_VARIABLE>
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /ecs/velo-admin-stg
        awslogs-region: us-east-1
        awslogs-stream-prefix: ecs

cpu: '1024'
memory: '2048'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>